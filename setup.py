#!/usr/bin/env python3
"""
Setup script for Personal Automation Assistant
Handles installation and initial configuration.
"""

import os
import sys
import subprocess
import platform


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def install_requirements():
    """Install required packages."""
    print("\n📦 Installing required packages...")
    
    try:
        # Install basic requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Basic requirements installed successfully!")
        
        # Try to install voice dependencies
        print("\n🎤 Installing voice capabilities...")
        try:
            if platform.system() == "Windows":
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyaudio"])
            else:
                print("⚠️  For voice features on macOS/Linux, you may need to install system dependencies:")
                print("   macOS: brew install portaudio")
                print("   Ubuntu/Debian: sudo apt-get install python3-pyaudio")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyaudio"])
            
            print("✅ Voice capabilities installed!")
            return True
            
        except subprocess.CalledProcessError:
            print("⚠️  Voice capabilities installation failed. You can still use text-only mode.")
            print("   To install voice features later, run: pip install pyaudio speechrecognition pyttsx3")
            return True
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = ["logs"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"📁 Directory already exists: {directory}")


def test_installation():
    """Test if installation was successful."""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        from automation import EmailAutomation, ReminderAutomation, BrowserAutomation, FileAutomation
        from logger_config import setup_logging
        print("✅ Core modules imported successfully")
        
        # Test voice (optional)
        try:
            from voice_handler import VoiceHandler
            voice_handler = VoiceHandler()
            if voice_handler.voice_available:
                print("✅ Voice capabilities available")
            else:
                print("⚠️  Voice capabilities not available (text-only mode)")
        except Exception:
            print("⚠️  Voice capabilities not available (text-only mode)")
        
        # Test logging
        logger = setup_logging()
        logger.info("Installation test")
        print("✅ Logging system working")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def setup_email_config():
    """Optional email configuration setup."""
    print("\n📧 Email Configuration (Optional)")
    print("You can configure email settings now or later through the assistant.")
    
    setup_now = input("Would you like to configure email settings now? (y/n): ").lower().strip()
    
    if setup_now.startswith('y'):
        try:
            from automation import EmailAutomation
            email_auto = EmailAutomation()
            email_auto.setup_email_config()
            print("✅ Email configuration completed!")
        except Exception as e:
            print(f"❌ Email configuration failed: {e}")
            print("You can configure email later by running the assistant.")


def main():
    """Main setup function."""
    print("🚀 Personal Automation Assistant - Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Setup failed during package installation.")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Test installation
    if not test_installation():
        print("\n❌ Setup failed during testing.")
        sys.exit(1)
    
    # Optional email setup
    setup_email_config()
    
    # Success message
    print("\n🎉 Setup completed successfully!")
    print("\n📋 What's next:")
    print("1. Run the assistant: python assistant.py")
    print("2. Try voice features (if available)")
    print("3. Configure email settings (if not done already)")
    print("4. Explore automation features")
    print("\n💡 Tips:")
    print("- Say 'help' to see available commands")
    print("- Check the logs/ directory for detailed logs")
    print("- Read README.md for detailed usage instructions")
    
    # Ask if user wants to start the assistant now
    start_now = input("\nWould you like to start the assistant now? (y/n): ").lower().strip()
    
    if start_now.startswith('y'):
        print("\n🚀 Starting Personal Automation Assistant...")
        try:
            import assistant
            assistant.main()
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
        except Exception as e:
            print(f"\n❌ Error starting assistant: {e}")


if __name__ == "__main__":
    main()
