#!/usr/bin/env python3
"""
Configuration file for JARVIS AI Assistant
Contains API keys, settings, and configuration options
"""

import os
import json
from typing import Dict, Any

class JarvisConfig:
    """Configuration manager for JARVIS"""
    
    def __init__(self, config_file: str = "jarvis_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self.get_default_config()
        else:
            config = self.get_default_config()
            self.save_config(config)
            return config
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "api": {
                "gemini_api_key": "",
                "gemini_model": "gemini-pro",
                "gemini_endpoint": "https://generativelanguage.googleapis.com/v1beta/models",
                "timeout": 30,
                "max_tokens": 1000
            },
            "voice": {
                "recognition_timeout": 5,
                "phrase_timeout": 10,
                "tts_rate": 180,
                "tts_volume": 0.9,
                "voice_enabled": True
            },
            "gui": {
                "theme": "dark",
                "window_width": 1200,
                "window_height": 800,
                "animation_speed": 50,
                "particle_count": 50
            },
            "assistant": {
                "name": "JARVIS",
                "personality": "professional",
                "response_style": "concise",
                "max_history": 100
            },
            "features": {
                "email_enabled": True,
                "reminders_enabled": True,
                "file_operations_enabled": True,
                "web_browsing_enabled": True,
                "voice_commands_enabled": True
            }
        }
    
    def save_config(self, config: Dict[str, Any] = None):
        """Save configuration to file"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'api.gemini_api_key')"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
        self.save_config()
    
    def setup_gemini_api(self):
        """Interactive setup for Gemini API"""
        print("\n🔧 Gemini API Configuration")
        print("=" * 40)
        print("To use JARVIS with Gemini AI, you need an API key.")
        print("Get your free API key at: https://makersuite.google.com/app/apikey")
        print()
        
        current_key = self.get("api.gemini_api_key", "")
        if current_key:
            print(f"Current API key: {current_key[:10]}...{current_key[-4:]}")
            update = input("Would you like to update it? (y/n): ").lower().strip()
            if not update.startswith('y'):
                return
        
        api_key = input("Enter your Gemini API key: ").strip()
        
        if api_key:
            self.set("api.gemini_api_key", api_key)
            print("✅ API key saved successfully!")
            
            # Test the API key
            if self.test_gemini_api():
                print("✅ API key is working!")
            else:
                print("⚠️  API key test failed. Please check your key.")
        else:
            print("❌ No API key provided.")
    
    def test_gemini_api(self) -> bool:
        """Test Gemini API connection"""
        try:
            import requests
            
            api_key = self.get("api.gemini_api_key")
            if not api_key:
                return False
            
            url = f"{self.get('api.gemini_endpoint')}/{self.get('api.gemini_model')}:generateContent?key={api_key}"
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": "Hello, this is a test message."
                    }]
                }]
            }
            
            response = requests.post(
                url, 
                json=payload, 
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"API test error: {e}")
            return False
    
    def setup_voice_settings(self):
        """Interactive voice settings configuration"""
        print("\n🎤 Voice Settings Configuration")
        print("=" * 40)
        
        # Voice recognition timeout
        current_timeout = self.get("voice.recognition_timeout", 5)
        print(f"Current recognition timeout: {current_timeout} seconds")
        new_timeout = input(f"Enter new timeout (or press Enter to keep {current_timeout}): ").strip()
        if new_timeout.isdigit():
            self.set("voice.recognition_timeout", int(new_timeout))
        
        # TTS rate
        current_rate = self.get("voice.tts_rate", 180)
        print(f"Current speech rate: {current_rate} words per minute")
        new_rate = input(f"Enter new rate (or press Enter to keep {current_rate}): ").strip()
        if new_rate.isdigit():
            self.set("voice.tts_rate", int(new_rate))
        
        # TTS volume
        current_volume = self.get("voice.tts_volume", 0.9)
        print(f"Current volume: {current_volume}")
        new_volume = input(f"Enter new volume 0.0-1.0 (or press Enter to keep {current_volume}): ").strip()
        try:
            volume = float(new_volume)
            if 0.0 <= volume <= 1.0:
                self.set("voice.tts_volume", volume)
        except ValueError:
            pass
        
        print("✅ Voice settings updated!")
    
    def setup_gui_settings(self):
        """Interactive GUI settings configuration"""
        print("\n🎨 GUI Settings Configuration")
        print("=" * 40)
        
        # Theme
        current_theme = self.get("gui.theme", "dark")
        print(f"Current theme: {current_theme}")
        new_theme = input("Enter theme (dark/light): ").strip().lower()
        if new_theme in ["dark", "light"]:
            self.set("gui.theme", new_theme)
        
        # Window size
        current_width = self.get("gui.window_width", 1200)
        current_height = self.get("gui.window_height", 800)
        print(f"Current window size: {current_width}x{current_height}")
        
        new_size = input("Enter new size (WIDTHxHEIGHT) or press Enter to keep current: ").strip()
        if 'x' in new_size:
            try:
                width, height = map(int, new_size.split('x'))
                self.set("gui.window_width", width)
                self.set("gui.window_height", height)
            except ValueError:
                pass
        
        print("✅ GUI settings updated!")
    
    def interactive_setup(self):
        """Run interactive configuration setup"""
        print("🚀 JARVIS Configuration Setup")
        print("=" * 50)
        
        while True:
            print("\nConfiguration Options:")
            print("1. Setup Gemini API")
            print("2. Configure Voice Settings")
            print("3. Configure GUI Settings")
            print("4. View Current Configuration")
            print("5. Reset to Defaults")
            print("0. Exit")
            
            choice = input("\nSelect an option (0-5): ").strip()
            
            if choice == "1":
                self.setup_gemini_api()
            elif choice == "2":
                self.setup_voice_settings()
            elif choice == "3":
                self.setup_gui_settings()
            elif choice == "4":
                self.display_config()
            elif choice == "5":
                self.reset_config()
            elif choice == "0":
                break
            else:
                print("Invalid option. Please try again.")
        
        print("\n✅ Configuration complete!")
    
    def display_config(self):
        """Display current configuration"""
        print("\n📋 Current Configuration:")
        print("=" * 40)
        print(json.dumps(self.config, indent=2))
    
    def reset_config(self):
        """Reset configuration to defaults"""
        confirm = input("Are you sure you want to reset all settings? (y/n): ").lower().strip()
        if confirm.startswith('y'):
            self.config = self.get_default_config()
            self.save_config()
            print("✅ Configuration reset to defaults!")

# Global configuration instance
jarvis_config = JarvisConfig()

def get_config():
    """Get the global configuration instance"""
    return jarvis_config

def main():
    """Main configuration setup"""
    config = JarvisConfig()
    config.interactive_setup()

if __name__ == "__main__":
    main()
