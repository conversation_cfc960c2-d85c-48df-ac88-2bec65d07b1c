#!/usr/bin/env python3
"""
Voice Engine - Speech Recognition and Text-to-Speech
Handles voice input/output with realistic TTS
"""

import speech_recognition as sr
import pyttsx3
import threading
import time
import json
import os
from typing import Optional

# Try to import Google Cloud TTS (optional)
try:
    from google.cloud import texttospeech
    GOOGLE_TTS_AVAILABLE = True
except ImportError:
    GOOGLE_TTS_AVAILABLE = False

# Try to import ElevenLabs (optional)
try:
    import elevenlabs
    ELEVENLABS_AVAILABLE = True
except ImportError:
    ELEVENLABS_AVAILABLE = False

class VoiceEngine:
    """Advanced voice engine with multiple TTS options"""
    
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.tts_engine = None
        self.tts_type = "pyttsx3"  # Default fallback
        
        # Voice settings
        self.recognition_timeout = 5
        self.phrase_timeout = 10
        self.tts_rate = 180
        self.tts_volume = 0.9
        
        # Load settings
        self.load_settings()
    
    def load_settings(self):
        """Load voice settings from config"""
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
                voice_settings = config.get('voice_settings', {})
                
                self.recognition_timeout = voice_settings.get('recognition_timeout', 5)
                self.phrase_timeout = voice_settings.get('phrase_timeout', 10)
                self.tts_rate = voice_settings.get('tts_rate', 180)
                self.tts_volume = voice_settings.get('tts_volume', 0.9)
                self.tts_type = voice_settings.get('tts_type', 'pyttsx3')
                
        except FileNotFoundError:
            pass
    
    def initialize(self):
        """Initialize voice components"""
        # Initialize speech recognition
        self.setup_speech_recognition()
        
        # Initialize TTS based on availability and preference
        self.setup_tts()
    
    def setup_speech_recognition(self):
        """Setup speech recognition"""
        try:
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            print("Calibrating microphone...")
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            
            # Configure recognizer
            self.recognizer.energy_threshold = 300
            self.recognizer.dynamic_energy_threshold = True
            self.recognizer.pause_threshold = 0.8
            
            print("Speech recognition initialized")
            
        except Exception as e:
            raise Exception(f"Failed to initialize speech recognition: {str(e)}")
    
    def setup_tts(self):
        """Setup text-to-speech engine"""
        # Try Google Cloud TTS first (best quality)
        if self.tts_type == "google" and GOOGLE_TTS_AVAILABLE:
            try:
                self.setup_google_tts()
                print("Google Cloud TTS initialized")
                return
            except Exception as e:
                print(f"Google TTS failed: {e}, falling back...")
        
        # Try ElevenLabs (high quality)
        if self.tts_type == "elevenlabs" and ELEVENLABS_AVAILABLE:
            try:
                self.setup_elevenlabs_tts()
                print("ElevenLabs TTS initialized")
                return
            except Exception as e:
                print(f"ElevenLabs TTS failed: {e}, falling back...")
        
        # Fallback to pyttsx3
        self.setup_pyttsx3_tts()
        print("pyttsx3 TTS initialized")
    
    def setup_google_tts(self):
        """Setup Google Cloud TTS"""
        # Check for credentials
        if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
            raise Exception("Google Cloud credentials not found")
        
        self.google_client = texttospeech.TextToSpeechClient()
        
        # Configure voice
        self.google_voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            name="en-US-Neural2-J",  # Male voice, similar to JARVIS
            ssml_gender=texttospeech.SsmlVoiceGender.MALE
        )
        
        self.google_audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3,
            speaking_rate=1.0,
            pitch=0.0
        )
        
        self.tts_type = "google"
    
    def setup_elevenlabs_tts(self):
        """Setup ElevenLabs TTS"""
        # Check for API key
        api_key = os.getenv('ELEVENLABS_API_KEY')
        if not api_key:
            try:
                with open('config.json', 'r') as f:
                    config = json.load(f)
                    api_key = config.get('elevenlabs_api_key')
            except:
                pass
        
        if not api_key:
            raise Exception("ElevenLabs API key not found")
        
        elevenlabs.set_api_key(api_key)
        
        # Use a professional male voice
        self.elevenlabs_voice = "Adam"  # Professional male voice
        self.tts_type = "elevenlabs"
    
    def setup_pyttsx3_tts(self):
        """Setup pyttsx3 TTS (fallback)"""
        self.tts_engine = pyttsx3.init()
        
        # Configure voice
        voices = self.tts_engine.getProperty('voices')
        
        # Try to find a male voice
        for voice in voices:
            if 'male' in voice.name.lower() or 'david' in voice.name.lower():
                self.tts_engine.setProperty('voice', voice.id)
                break
        
        # Set properties
        self.tts_engine.setProperty('rate', self.tts_rate)
        self.tts_engine.setProperty('volume', self.tts_volume)
        
        self.tts_type = "pyttsx3"
    
    def listen(self) -> Optional[str]:
        """Listen for voice input and return text"""
        if not self.microphone:
            raise Exception("Microphone not initialized")
        
        try:
            print("Listening...")
            
            with self.microphone as source:
                # Listen for audio
                audio = self.recognizer.listen(
                    source,
                    timeout=self.recognition_timeout,
                    phrase_time_limit=self.phrase_timeout
                )
            
            print("Processing speech...")
            
            # Try Google Speech Recognition first
            try:
                text = self.recognizer.recognize_google(audio)
                print(f"Recognized: {text}")
                return text
            
            except sr.UnknownValueError:
                print("Could not understand audio")
                return None
            
            except sr.RequestError as e:
                print(f"Google Speech Recognition error: {e}")
                
                # Fallback to offline recognition
                try:
                    text = self.recognizer.recognize_sphinx(audio)
                    print(f"Recognized (offline): {text}")
                    return text
                except:
                    print("Offline recognition also failed")
                    return None
        
        except sr.WaitTimeoutError:
            print("Listening timeout")
            return None
        
        except Exception as e:
            print(f"Speech recognition error: {e}")
            return None
    
    def speak(self, text: str):
        """Convert text to speech and play"""
        if not text.strip():
            return
        
        try:
            if self.tts_type == "google":
                self.speak_google(text)
            elif self.tts_type == "elevenlabs":
                self.speak_elevenlabs(text)
            else:
                self.speak_pyttsx3(text)
                
        except Exception as e:
            print(f"TTS error: {e}")
            # Fallback to pyttsx3
            if self.tts_type != "pyttsx3":
                self.speak_pyttsx3(text)
    
    def speak_google(self, text: str):
        """Speak using Google Cloud TTS"""
        synthesis_input = texttospeech.SynthesisInput(text=text)
        
        response = self.google_client.synthesize_speech(
            input=synthesis_input,
            voice=self.google_voice,
            audio_config=self.google_audio_config
        )
        
        # Play audio
        import io
        import pygame
        
        pygame.mixer.init()
        audio_data = io.BytesIO(response.audio_content)
        pygame.mixer.music.load(audio_data)
        pygame.mixer.music.play()
        
        # Wait for playback to finish
        while pygame.mixer.music.get_busy():
            time.sleep(0.1)
    
    def speak_elevenlabs(self, text: str):
        """Speak using ElevenLabs TTS"""
        audio = elevenlabs.generate(
            text=text,
            voice=self.elevenlabs_voice,
            model="eleven_monolingual_v1"
        )
        
        # Play audio
        import io
        import pygame
        
        pygame.mixer.init()
        audio_data = io.BytesIO(audio)
        pygame.mixer.music.load(audio_data)
        pygame.mixer.music.play()
        
        # Wait for playback to finish
        while pygame.mixer.music.get_busy():
            time.sleep(0.1)
    
    def speak_pyttsx3(self, text: str):
        """Speak using pyttsx3 (fallback)"""
        if not self.tts_engine:
            self.setup_pyttsx3_tts()
        
        self.tts_engine.say(text)
        self.tts_engine.runAndWait()
    
    def test_voice(self):
        """Test voice functionality"""
        print("Testing TTS...")
        self.speak("JARVIS systems online. Voice test successful.")
        
        print("Testing speech recognition...")
        print("Say something...")
        result = self.listen()
        
        if result:
            print(f"You said: {result}")
            self.speak(f"I heard you say: {result}")
            return True
        else:
            print("Speech recognition test failed")
            return False

def create_voice_config():
    """Create voice configuration template"""
    config = {
        "voice_settings": {
            "recognition_timeout": 5,
            "phrase_timeout": 10,
            "tts_rate": 180,
            "tts_volume": 0.9,
            "tts_type": "pyttsx3"
        },
        "elevenlabs_api_key": "YOUR_ELEVENLABS_API_KEY_HERE"
    }
    
    try:
        with open('config.json', 'r') as f:
            existing_config = json.load(f)
            existing_config.update(config)
            config = existing_config
    except FileNotFoundError:
        pass
    
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("Voice configuration added to config.json")

if __name__ == "__main__":
    # Test voice engine
    try:
        engine = VoiceEngine()
        engine.initialize()
        engine.test_voice()
    except Exception as e:
        print(f"Error: {e}")
        create_voice_config()
