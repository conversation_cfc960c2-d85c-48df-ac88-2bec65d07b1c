# JARVIS AI Assistant - Complete Requirements
# Core dependencies for full functionality

# GUI Framework
kivy>=2.2.0
kivymd>=1.1.1

# Alternative GUI (optional)
customtkinter>=5.2.0

# AI and API
requests>=2.28.0
google-generativeai>=0.3.0

# Voice Recognition and TTS
speechrecognition>=3.10.0
pyttsx3>=2.90
pyaudio>=0.2.11

# Advanced TTS Options (optional)
google-cloud-texttospeech>=2.14.0
elevenlabs>=0.2.24

# Audio processing
pygame>=2.5.0

# Automation
schedule>=1.2.0

# Image processing for GUI
pillow>=10.0.0

# Math and animations
numpy>=1.24.0

# Email functionality
# smtplib is built-in

# File operations
# os, shutil are built-in

# Date and time
# datetime is built-in

# Threading and async
# threading, asyncio are built-in

# JSON handling
# json is built-in

# Regular expressions
# re is built-in

# Development and testing (optional)
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0

# Mobile deployment (optional)
buildozer>=1.5.0
python-for-android>=2023.3.0

# Windows desktop shortcuts (optional)
pywin32>=306; sys_platform == "win32"
winshell>=0.6; sys_platform == "win32"
