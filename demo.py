#!/usr/bin/env python3
"""
Demo script for Personal Automation Assistant
Showcases the assistant's capabilities with automated demonstrations.
"""

import time
import os
from automation import EmailAutomation, ReminderAutomation, BrowserAutomation, FileAutomation
from voice_handler import VoiceHandler
from logger_config import setup_logging, ActionLogger


def print_demo_header(title):
    """Print a formatted demo section header."""
    print("\n" + "=" * 60)
    print(f"🎬 DEMO: {title}")
    print("=" * 60)


def demo_email_automation():
    """Demonstrate email automation capabilities."""
    print_demo_header("Email Automation")
    
    email_auto = EmailAutomation()
    
    print("📧 Email Automation Features:")
    print("• Send emails with custom recipients, subjects, and messages")
    print("• Support for Gmail with App Password authentication")
    print("• Automatic SMTP configuration")
    
    print("\n📋 Example Usage:")
    print("User: 'Send an <NAME_EMAIL>'")
    print("Assistant: 'I can help you send an email! Let me get the details.'")
    print("Assistant: 'Who would you like to send the email to?'")
    print("User: '<EMAIL>'")
    print("Assistant: 'What's the subject?'")
    print("User: 'Meeting Tomorrow'")
    print("Assistant: 'What would you like to say?'")
    print("User: 'Don't forget about our meeting tomorrow at 2 PM.'")
    print("Assistant: 'I'll send an <NAME_EMAIL> with the subject 'Meeting Tomorrow'. Shall I go ahead?'")
    print("User: 'yes'")
    print("Assistant: 'Great! Email sent successfully!'")
    
    # Test email configuration loading
    config = email_auto._load_email_config()
    if config.get("email"):
        print(f"\n✅ Email configured for: {config['email']}")
    else:
        print("\n⚠️  Email not configured. Run setup to configure email settings.")


def demo_reminder_automation():
    """Demonstrate reminder automation capabilities."""
    print_demo_header("Reminder Automation")
    
    reminder_auto = ReminderAutomation()
    
    print("⏰ Reminder Features:")
    print("• Set reminders with natural language time input")
    print("• Support for minutes, hours, and specific times")
    print("• Background scheduling and notifications")
    
    print("\n📋 Example Usage:")
    print("User: 'Remind me to call mom in 30 minutes'")
    print("Assistant: 'I'll help you set a reminder!'")
    print("Assistant: 'What would you like me to remind you about?'")
    print("User: 'call mom'")
    print("Assistant: 'When should I remind you?'")
    print("User: '30 minutes'")
    print("Assistant: 'I'll remind you about 'call mom' in 30 minutes. Should I proceed?'")
    print("User: 'yes'")
    print("Assistant: 'Reminder set for 2024-01-15 15:30: 'call mom''")
    
    # Demonstrate time parsing
    print("\n🕐 Time Parsing Examples:")
    test_times = ["5 minutes", "2 hours", "tomorrow 9am"]
    
    for time_input in test_times:
        parsed_time = reminder_auto._parse_time_input(time_input)
        print(f"• '{time_input}' → {parsed_time.strftime('%Y-%m-%d %H:%M')}")
    
    # Set a demo reminder
    print("\n🧪 Setting a demo reminder for 1 minute from now...")
    result = reminder_auto.set_reminder("Demo reminder - this is a test!", "1 minute")
    if result["success"]:
        print(f"✅ {result['message']}")
    else:
        print(f"❌ {result['message']}")


def demo_browser_automation():
    """Demonstrate browser automation capabilities."""
    print_demo_header("Browser Automation")
    
    browser_auto = BrowserAutomation()
    
    print("🌐 Browser Features:")
    print("• Open any website in default browser")
    print("• Automatic protocol detection (adds https:// if missing)")
    print("• Google search functionality")
    
    print("\n📋 Example Usage:")
    print("User: 'Open Google'")
    print("Assistant: 'What website would you like me to open?'")
    print("User: 'google.com'")
    print("Assistant: 'I'll open google.com in your browser. Ready to execute?'")
    print("User: 'yes'")
    print("Assistant: 'Opened https://google.com in your default browser!'")
    
    print("\n🧪 Testing browser automation...")
    
    # Test URL opening (won't actually open browser in demo)
    test_urls = ["google.com", "https://github.com", "stackoverflow.com"]
    
    for url in test_urls:
        print(f"• Testing URL: {url}")
        # We'll just test the URL formatting logic
        formatted_url = url if url.startswith(('http://', 'https://')) else 'https://' + url
        print(f"  → Formatted as: {formatted_url}")
    
    print("✅ Browser automation ready!")


def demo_file_automation():
    """Demonstrate file automation capabilities."""
    print_demo_header("File Automation")
    
    file_auto = FileAutomation()
    
    print("📁 File Management Features:")
    print("• Rename files and directories")
    print("• List directory contents")
    print("• Create new directories")
    print("• Safe file operations with error handling")
    
    print("\n📋 Example Usage:")
    print("User: 'Rename a file'")
    print("Assistant: 'I can help you with file operations!'")
    print("Assistant: 'What would you like to do?'")
    print("User: 'rename'")
    print("Assistant: 'What's the file path?'")
    print("User: '/path/to/old_report.txt'")
    print("Assistant: 'What should the new name be?'")
    print("User: 'new_report.txt'")
    print("Assistant: 'I'll rename '/path/to/old_report.txt' to 'new_report.txt'. Should I continue?'")
    print("User: 'yes'")
    print("Assistant: 'Successfully renamed the file!'")
    
    print("\n🧪 Testing file operations...")
    
    # List current directory
    result = file_auto.list_files(".")
    if result["success"]:
        print(f"✅ Found {len(result['data'])} items in current directory")
        print("📄 Files in current directory:")
        for item in result["data"][:5]:  # Show first 5 items
            icon = "📁" if item["is_directory"] else "📄"
            print(f"  {icon} {item['name']}")
        if len(result["data"]) > 5:
            print(f"  ... and {len(result['data']) - 5} more items")
    else:
        print(f"❌ {result['message']}")


def demo_voice_capabilities():
    """Demonstrate voice capabilities."""
    print_demo_header("Voice Capabilities")
    
    print("🎤 Voice Features:")
    print("• Speech recognition using Google's API")
    print("• Text-to-speech with configurable voices")
    print("• Automatic microphone calibration")
    print("• Fallback to text mode if voice unavailable")
    
    try:
        voice_handler = VoiceHandler()
        
        if voice_handler.voice_available:
            print("\n✅ Voice capabilities are available!")
            
            print("\n🔊 Available TTS voices:")
            voice_handler.list_available_voices()
            
            print("\n🧪 Testing text-to-speech...")
            voice_handler.speak("Hello! This is a demonstration of the voice capabilities.")
            
            print("\n📋 Voice Interaction Example:")
            print("Assistant: 'What would you like me to do?' (spoken)")
            print("User: 'Send an email' (spoken)")
            print("Assistant: 'I can help you send an email!' (spoken)")
            
        else:
            print("\n⚠️  Voice capabilities not available")
            print("To enable voice features:")
            print("1. Install dependencies: pip install speechrecognition pyttsx3 pyaudio")
            print("2. On macOS: brew install portaudio")
            print("3. On Linux: sudo apt-get install python3-pyaudio")
            
    except Exception as e:
        print(f"\n❌ Voice demo failed: {e}")


def demo_logging_system():
    """Demonstrate logging capabilities."""
    print_demo_header("Logging System")
    
    print("📊 Logging Features:")
    print("• Comprehensive action logging")
    print("• Session tracking and summaries")
    print("• Error logging and debugging")
    print("• Exportable interaction history")
    
    # Set up demo logger
    logger = setup_logging()
    action_logger = ActionLogger()
    
    print("\n🧪 Testing logging system...")
    
    # Log some demo actions
    action_logger.log_user_input("Demo user input", "text")
    action_logger.log_assistant_response("Demo assistant response", "text")
    action_logger.log_command_execution("demo_command", {"param": "value"}, {"success": True, "message": "Demo successful"})
    
    # Get session summary
    summary = action_logger.get_session_summary()
    
    print("📈 Current Session Summary:")
    for key, value in summary.items():
        if key != "session_id":
            print(f"  • {key.replace('_', ' ').title()}: {value}")
    
    # Check log files
    log_dir = "logs"
    if os.path.exists(log_dir):
        log_files = os.listdir(log_dir)
        print(f"\n📁 Log files created: {len(log_files)}")
        for log_file in log_files:
            print(f"  • {log_file}")
    
    print("✅ Logging system working perfectly!")


def demo_conversation_flow():
    """Demonstrate natural conversation flow."""
    print_demo_header("Natural Conversation Flow")
    
    print("💬 Conversation Features:")
    print("• Human-like, friendly interaction style")
    print("• Confirmation requests before actions")
    print("• Context-aware responses")
    print("• Graceful error handling")
    
    print("\n🎭 Sample Conversation:")
    print("=" * 40)
    
    conversation = [
        ("🤖", "Hello! I'm your personal assistant. What would you like me to help you with today?"),
        ("👤", "Hi! What should I call you?"),
        ("🤖", "You can call me Assistant! What's your name?"),
        ("👤", "I'm Alex."),
        ("🤖", "Nice to meet you, Alex! I'm excited to help you today."),
        ("👤", "Can you help me send an email?"),
        ("🤖", "I can help you send an email! Let me get the details."),
        ("👤", "Actually, never mind."),
        ("🤖", "No problem! Task cancelled. Anything else I can help with?"),
        ("👤", "What can you do?"),
        ("🤖", "Here are the things I can help you with:\n• Send emails\n• Set reminders\n• Open websites\n• Rename files\n• Have friendly conversations!"),
        ("👤", "That's awesome!"),
        ("🤖", "Thank you, Alex! I'm here whenever you need assistance."),
    ]
    
    for speaker, message in conversation:
        print(f"{speaker} {message}")
        time.sleep(1)  # Simulate conversation timing
    
    print("=" * 40)
    print("✅ Natural conversation flow demonstrated!")


def main():
    """Main demo function."""
    print("🎬 Personal Automation Assistant - Interactive Demo")
    print("=" * 60)
    print("This demo showcases all the capabilities of your new assistant!")
    
    demos = [
        ("Conversation Flow", demo_conversation_flow),
        ("Email Automation", demo_email_automation),
        ("Reminder System", demo_reminder_automation),
        ("Browser Control", demo_browser_automation),
        ("File Management", demo_file_automation),
        ("Voice Capabilities", demo_voice_capabilities),
        ("Logging System", demo_logging_system),
    ]
    
    print("\n📋 Available Demos:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"{i}. {name}")
    print("0. Run all demos")
    
    try:
        choice = input("\nWhich demo would you like to see? (0-7): ").strip()
        
        if choice == "0":
            print("\n🚀 Running all demos...")
            for name, demo_func in demos:
                demo_func()
                input("\nPress Enter to continue to next demo...")
        elif choice.isdigit() and 1 <= int(choice) <= len(demos):
            name, demo_func = demos[int(choice) - 1]
            print(f"\n🚀 Running {name} demo...")
            demo_func()
        else:
            print("❌ Invalid choice. Please run the demo again.")
            return
        
        print("\n🎉 Demo completed!")
        print("\n💡 Ready to try the real assistant?")
        print("Run: python assistant.py")
        
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")


if __name__ == "__main__":
    main()
