#!/usr/bin/env python3
"""
Jarvis-Style AI Assistant GUI
A futuristic, animated interface for voice-controlled AI assistant powered by Gemini API
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import scrolledtext
import threading
import time
import math
import random
from datetime import datetime
import speech_recognition as sr
import pyttsx3
import requests
import json
import queue
import numpy as np
from PIL import Image, ImageTk
import io

# Configure CustomTkinter
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class JarvisGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("JARVIS - AI Assistant")
        self.root.geometry("1200x800")
        self.root.configure(fg_color=("#0a0a0a", "#0a0a0a"))
        
        # Initialize components
        self.setup_voice_components()
        self.setup_variables()
        self.create_gui()
        self.start_background_animations()
        
        # Message queue for thread communication
        self.message_queue = queue.Queue()
        self.root.after(100, self.process_queue)
    
    def setup_voice_components(self):
        """Initialize speech recognition and TTS"""
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Initialize TTS
        self.tts_engine = pyttsx3.init()
        self.tts_engine.setProperty('rate', 180)
        self.tts_engine.setProperty('volume', 0.9)
        
        # Adjust for ambient noise
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source, duration=1)
    
    def setup_variables(self):
        """Initialize GUI variables"""
        self.is_listening = False
        self.is_speaking = False
        self.dark_mode = True
        self.conversation_history = []
        self.command_history = []
        
        # Animation variables
        self.pulse_angle = 0
        self.wave_offset = 0
        self.particle_positions = []
        
        # Initialize particles
        for _ in range(50):
            self.particle_positions.append({
                'x': random.randint(0, 1200),
                'y': random.randint(0, 800),
                'speed': random.uniform(0.5, 2.0),
                'angle': random.uniform(0, 2 * math.pi)
            })
    
    def create_gui(self):
        """Create the main GUI layout"""
        # Main container
        self.main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Create header
        self.create_header()
        
        # Create main content area
        self.create_main_content()
        
        # Create control panel
        self.create_control_panel()
        
        # Create status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create the header with title and controls"""
        header_frame = ctk.CTkFrame(self.main_frame, height=80, fg_color=("#1a1a1a", "#1a1a1a"))
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Title with glow effect
        title_label = ctk.CTkLabel(
            header_frame,
            text="J.A.R.V.I.S",
            font=ctk.CTkFont(family="Arial", size=36, weight="bold"),
            text_color=("#00d4ff", "#00d4ff")
        )
        title_label.pack(side="left", padx=20, pady=20)
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="Just A Rather Very Intelligent System",
            font=ctk.CTkFont(family="Arial", size=14),
            text_color=("#888888", "#888888")
        )
        subtitle_label.pack(side="left", padx=(0, 20), pady=20)
        
        # Dark mode toggle
        self.dark_mode_switch = ctk.CTkSwitch(
            header_frame,
            text="Dark Mode",
            command=self.toggle_dark_mode,
            progress_color=("#00d4ff", "#00d4ff")
        )
        self.dark_mode_switch.pack(side="right", padx=20, pady=20)
        self.dark_mode_switch.select()
    
    def create_main_content(self):
        """Create the main content area with chat and controls"""
        content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        content_frame.pack(fill="both", expand=True)
        
        # Left panel - Chat area
        self.create_chat_panel(content_frame)
        
        # Right panel - Assistant avatar and controls
        self.create_assistant_panel(content_frame)
    
    def create_chat_panel(self, parent):
        """Create the chat conversation panel"""
        chat_frame = ctk.CTkFrame(parent, fg_color=("#1a1a1a", "#1a1a1a"))
        chat_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # Chat header
        chat_header = ctk.CTkLabel(
            chat_frame,
            text="Conversation",
            font=ctk.CTkFont(family="Arial", size=18, weight="bold"),
            text_color=("#00d4ff", "#00d4ff")
        )
        chat_header.pack(pady=(20, 10))
        
        # Chat display area
        self.chat_display = ctk.CTkTextbox(
            chat_frame,
            height=400,
            font=ctk.CTkFont(family="Consolas", size=12),
            fg_color=("#0a0a0a", "#0a0a0a"),
            text_color=("#ffffff", "#ffffff"),
            scrollbar_button_color=("#00d4ff", "#00d4ff")
        )
        self.chat_display.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Add welcome message
        self.add_message("JARVIS", "Good day! I'm JARVIS, your AI assistant. How may I help you today?", "assistant")
    
    def create_assistant_panel(self, parent):
        """Create the assistant avatar and control panel"""
        assistant_frame = ctk.CTkFrame(parent, width=400, fg_color=("#1a1a1a", "#1a1a1a"))
        assistant_frame.pack(side="right", fill="y")
        assistant_frame.pack_propagate(False)
        
        # Assistant avatar area
        avatar_frame = ctk.CTkFrame(assistant_frame, height=300, fg_color="transparent")
        avatar_frame.pack(fill="x", padx=20, pady=20)
        avatar_frame.pack_propagate(False)
        
        # Create canvas for avatar animation
        self.avatar_canvas = tk.Canvas(
            avatar_frame,
            width=360,
            height=280,
            bg="#1a1a1a",
            highlightthickness=0
        )
        self.avatar_canvas.pack()
        
        # Microphone button
        self.create_mic_button(assistant_frame)
        
        # Voice waveform display
        self.create_waveform_display(assistant_frame)
        
        # Command history
        self.create_command_history(assistant_frame)
    
    def create_mic_button(self, parent):
        """Create the animated microphone button"""
        mic_frame = ctk.CTkFrame(parent, height=120, fg_color="transparent")
        mic_frame.pack(fill="x", padx=20, pady=10)
        mic_frame.pack_propagate(False)
        
        self.mic_button = ctk.CTkButton(
            mic_frame,
            text="🎤",
            width=80,
            height=80,
            corner_radius=40,
            font=ctk.CTkFont(size=30),
            fg_color=("#00d4ff", "#00d4ff"),
            hover_color=("#0099cc", "#0099cc"),
            command=self.toggle_listening
        )
        self.mic_button.pack(pady=20)
        
        # Status label
        self.mic_status = ctk.CTkLabel(
            mic_frame,
            text="Click to speak",
            font=ctk.CTkFont(size=12),
            text_color=("#888888", "#888888")
        )
        self.mic_status.pack()
    
    def create_waveform_display(self, parent):
        """Create voice waveform visualization"""
        wave_frame = ctk.CTkFrame(parent, height=100, fg_color=("#0a0a0a", "#0a0a0a"))
        wave_frame.pack(fill="x", padx=20, pady=10)
        wave_frame.pack_propagate(False)
        
        self.waveform_canvas = tk.Canvas(
            wave_frame,
            width=360,
            height=80,
            bg="#0a0a0a",
            highlightthickness=0
        )
        self.waveform_canvas.pack(pady=10)
    
    def create_command_history(self, parent):
        """Create command history panel"""
        history_frame = ctk.CTkFrame(parent, fg_color=("#0a0a0a", "#0a0a0a"))
        history_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        history_label = ctk.CTkLabel(
            history_frame,
            text="Recent Commands",
            font=ctk.CTkFont(family="Arial", size=14, weight="bold"),
            text_color=("#00d4ff", "#00d4ff")
        )
        history_label.pack(pady=(10, 5))
        
        self.history_display = ctk.CTkTextbox(
            history_frame,
            height=150,
            font=ctk.CTkFont(family="Consolas", size=10),
            fg_color=("#1a1a1a", "#1a1a1a"),
            text_color=("#888888", "#888888")
        )
        self.history_display.pack(fill="both", expand=True, padx=10, pady=(0, 10))
    
    def create_control_panel(self):
        """Create bottom control panel"""
        control_frame = ctk.CTkFrame(self.main_frame, height=60, fg_color=("#1a1a1a", "#1a1a1a"))
        control_frame.pack(fill="x", pady=(20, 0))
        control_frame.pack_propagate(False)
        
        # Text input for manual commands
        self.text_input = ctk.CTkEntry(
            control_frame,
            placeholder_text="Type your message here...",
            font=ctk.CTkFont(size=12),
            height=40
        )
        self.text_input.pack(side="left", fill="x", expand=True, padx=(20, 10), pady=10)
        self.text_input.bind("<Return>", self.send_text_message)
        
        # Send button
        send_button = ctk.CTkButton(
            control_frame,
            text="Send",
            width=80,
            height=40,
            command=self.send_text_message,
            fg_color=("#00d4ff", "#00d4ff"),
            hover_color=("#0099cc", "#0099cc")
        )
        send_button.pack(side="right", padx=(0, 20), pady=10)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = ctk.CTkFrame(self.main_frame, height=30, fg_color=("#0a0a0a", "#0a0a0a"))
        self.status_frame.pack(fill="x", pady=(10, 0))
        self.status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            font=ctk.CTkFont(size=10),
            text_color=("#888888", "#888888")
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Time display
        self.time_label = ctk.CTkLabel(
            self.status_frame,
            text="",
            font=ctk.CTkFont(size=10),
            text_color=("#888888", "#888888")
        )
        self.time_label.pack(side="right", padx=10, pady=5)
        
        self.update_time()
    
    def add_message(self, sender, message, msg_type="user"):
        """Add message to chat display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if msg_type == "assistant":
            color_code = "#00d4ff"
            prefix = "🤖"
        else:
            color_code = "#ffffff"
            prefix = "👤"
        
        formatted_message = f"[{timestamp}] {prefix} {sender}: {message}\n\n"
        
        self.chat_display.insert("end", formatted_message)
        self.chat_display.see("end")
        
        # Store in conversation history
        self.conversation_history.append({
            'sender': sender,
            'message': message,
            'type': msg_type,
            'timestamp': timestamp
        })
    
    def toggle_listening(self):
        """Toggle voice listening state"""
        if not self.is_listening:
            self.start_listening()
        else:
            self.stop_listening()
    
    def start_listening(self):
        """Start voice recognition"""
        self.is_listening = True
        self.mic_button.configure(fg_color=("#ff4444", "#ff4444"), text="🔴")
        self.mic_status.configure(text="Listening...")
        self.update_status("Listening for voice input...")
        
        # Start listening in separate thread
        threading.Thread(target=self.listen_for_voice, daemon=True).start()
    
    def stop_listening(self):
        """Stop voice recognition"""
        self.is_listening = False
        self.mic_button.configure(fg_color=("#00d4ff", "#00d4ff"), text="🎤")
        self.mic_status.configure(text="Click to speak")
        self.update_status("Ready")
    
    def listen_for_voice(self):
        """Voice recognition thread"""
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=10)
            
            self.update_status("Processing speech...")
            text = self.recognizer.recognize_google(audio)
            
            # Add user message to chat
            self.message_queue.put(('add_message', ('You', text, 'user')))
            
            # Process with Gemini API
            self.message_queue.put(('process_with_gemini', text))
            
        except sr.WaitTimeoutError:
            self.message_queue.put(('update_status', 'No speech detected'))
        except sr.UnknownValueError:
            self.message_queue.put(('update_status', 'Could not understand audio'))
        except Exception as e:
            self.message_queue.put(('update_status', f'Error: {str(e)}'))
        finally:
            self.message_queue.put(('stop_listening',))
    
    def send_text_message(self, event=None):
        """Send text message"""
        message = self.text_input.get().strip()
        if message:
            self.add_message("You", message, "user")
            self.text_input.delete(0, "end")
            
            # Process with Gemini API
            threading.Thread(target=self.process_with_gemini, args=(message,), daemon=True).start()
    
    def process_with_gemini(self, user_message):
        """Process message with Gemini API"""
        try:
            self.message_queue.put(('update_status', 'Thinking...'))

            # Import config
            from config import get_config
            config = get_config()

            api_key = config.get("api.gemini_api_key")
            if not api_key:
                error_msg = "Gemini API key not configured. Please run config.py to set it up."
                self.message_queue.put(('add_message', ('JARVIS', error_msg, 'assistant')))
                return

            model = config.get("api.gemini_model", "gemini-pro")
            endpoint = config.get("api.gemini_endpoint")
            timeout = config.get("api.timeout", 30)

            url = f"{endpoint}/{model}:generateContent?key={api_key}"

            # Enhanced prompt for JARVIS personality
            enhanced_prompt = f"""You are JARVIS, an advanced AI assistant inspired by Tony Stark's AI.
            Respond in a professional, intelligent, and slightly witty manner.
            Be helpful and concise. User message: {user_message}"""

            payload = {
                "contents": [{
                    "parts": [{
                        "text": enhanced_prompt
                    }]
                }],
                "generationConfig": {
                    "maxOutputTokens": config.get("api.max_tokens", 1000),
                    "temperature": 0.7
                }
            }

            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(url, json=payload, headers=headers, timeout=timeout)

            if response.status_code == 200:
                result = response.json()
                ai_response = result['candidates'][0]['content']['parts'][0]['text']

                # Add AI response to chat
                self.message_queue.put(('add_message', ('JARVIS', ai_response, 'assistant')))

                # Speak the response
                self.message_queue.put(('speak_response', ai_response))

                # Add to command history
                self.message_queue.put(('add_command', f"User: {user_message[:50]}..."))

            else:
                error_msg = "I'm experiencing some technical difficulties. Please try again."
                self.message_queue.put(('add_message', ('JARVIS', error_msg, 'assistant')))

        except Exception as e:
            error_msg = f"I encountered an error: {str(e)}"
            self.message_queue.put(('add_message', ('JARVIS', error_msg, 'assistant')))
        finally:
            self.message_queue.put(('update_status', 'Ready'))
    
    def speak_response(self, text):
        """Speak AI response using TTS"""
        def speak():
            self.is_speaking = True
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()
            self.is_speaking = False
        
        threading.Thread(target=speak, daemon=True).start()
    
    def add_command(self, command):
        """Add command to history"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.command_history.append(f"[{timestamp}] {command}")
        
        # Update history display
        self.history_display.delete("1.0", "end")
        for cmd in self.command_history[-10:]:  # Show last 10 commands
            self.history_display.insert("end", cmd + "\n")
        self.history_display.see("end")
    
    def update_status(self, status):
        """Update status bar"""
        self.status_label.configure(text=status)
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.configure(text=current_time)
        self.root.after(1000, self.update_time)
    
    def toggle_dark_mode(self):
        """Toggle dark/light mode"""
        self.dark_mode = not self.dark_mode
        if self.dark_mode:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")
    
    def start_background_animations(self):
        """Start background animations"""
        self.animate_avatar()
        self.animate_waveform()
        self.animate_particles()
    
    def animate_avatar(self):
        """Animate the assistant avatar"""
        self.avatar_canvas.delete("all")
        
        # Draw pulsing circle
        center_x, center_y = 180, 140
        radius = 60 + 10 * math.sin(self.pulse_angle)
        
        # Outer glow
        for i in range(5):
            alpha_radius = radius + i * 5
            color_intensity = int(255 * (1 - i * 0.2))
            color = f"#{color_intensity:02x}{color_intensity:02x}ff"
            self.avatar_canvas.create_oval(
                center_x - alpha_radius, center_y - alpha_radius,
                center_x + alpha_radius, center_y + alpha_radius,
                outline=color, width=1
            )
        
        # Main circle
        self.avatar_canvas.create_oval(
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius,
            outline="#00d4ff", width=3, fill="#001122"
        )
        
        # Inner patterns
        for i in range(8):
            angle = self.pulse_angle + i * math.pi / 4
            x1 = center_x + 30 * math.cos(angle)
            y1 = center_y + 30 * math.sin(angle)
            x2 = center_x + 45 * math.cos(angle)
            y2 = center_y + 45 * math.sin(angle)
            self.avatar_canvas.create_line(x1, y1, x2, y2, fill="#00d4ff", width=2)
        
        self.pulse_angle += 0.1
        self.root.after(50, self.animate_avatar)
    
    def animate_waveform(self):
        """Animate voice waveform"""
        self.waveform_canvas.delete("all")
        
        if self.is_listening or self.is_speaking:
            # Draw animated waveform
            for x in range(0, 360, 5):
                height = 20 + 15 * math.sin((x + self.wave_offset) * 0.1) * random.uniform(0.5, 1.5)
                self.waveform_canvas.create_line(
                    x, 40 - height, x, 40 + height,
                    fill="#00d4ff", width=2
                )
        else:
            # Draw flat line when inactive
            self.waveform_canvas.create_line(0, 40, 360, 40, fill="#444444", width=1)
        
        self.wave_offset += 5
        self.root.after(100, self.animate_waveform)
    
    def animate_particles(self):
        """Animate background particles"""
        # This would be implemented on the main canvas background
        # For now, we'll skip this to keep the code under 300 lines
        self.root.after(100, self.animate_particles)
    
    def process_queue(self):
        """Process messages from worker threads"""
        try:
            while True:
                action, *args = self.message_queue.get_nowait()
                
                if action == 'add_message':
                    self.add_message(*args[0])
                elif action == 'update_status':
                    self.update_status(args[0])
                elif action == 'stop_listening':
                    self.stop_listening()
                elif action == 'speak_response':
                    self.speak_response(args[0])
                elif action == 'add_command':
                    self.add_command(args[0])
                elif action == 'process_with_gemini':
                    threading.Thread(target=self.process_with_gemini, args=(args[0],), daemon=True).start()
                    
        except queue.Empty:
            pass
        
        self.root.after(100, self.process_queue)
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()

def main():
    """Main entry point"""
    print("🚀 Starting JARVIS GUI...")
    app = JarvisGUI()
    app.run()

if __name__ == "__main__":
    main()
