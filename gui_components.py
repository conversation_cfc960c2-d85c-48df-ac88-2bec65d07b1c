#!/usr/bin/env python3
"""
GUI Components - Animated UI elements for JARVIS
Custom Kivy widgets with sci-fi animations
"""

import math
import random
from kivy.uix.widget import Widget
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.graphics import Color, Ellipse, Line, Rectangle, PushMatrix, PopMatrix, Rotate
from kivy.animation import Animation
from kivy.clock import Clock
from kivy.metrics import dp

class AnimatedBackground(Widget):
    """Animated sci-fi background with particles and grid"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Animation state
        self.particles = []
        self.grid_offset = 0
        self.pulse_angle = 0
        
        # Initialize particles
        self.init_particles()
        
        # Bind to size changes
        self.bind(size=self.update_graphics)
        
        # Start animation
        Clock.schedule_interval(self.animate, 1/30)  # 30 FPS
    
    def init_particles(self):
        """Initialize floating particles"""
        for _ in range(30):
            particle = {
                'x': random.uniform(0, 800),
                'y': random.uniform(0, 600),
                'speed': random.uniform(0.5, 2.0),
                'angle': random.uniform(0, 2 * math.pi),
                'size': random.uniform(2, 6),
                'alpha': random.uniform(0.3, 0.8)
            }
            self.particles.append(particle)
    
    def update_graphics(self, *args):
        """Update graphics when size changes"""
        self.canvas.clear()
        self.draw_background()
    
    def draw_background(self):
        """Draw the animated background"""
        with self.canvas:
            # Dark background
            Color(0.05, 0.05, 0.1, 1)
            Rectangle(pos=self.pos, size=self.size)
            
            # Grid lines
            self.draw_grid()
            
            # Particles
            self.draw_particles()
            
            # Pulse effect
            self.draw_pulse()
    
    def draw_grid(self):
        """Draw animated grid lines"""
        Color(0, 0.3, 0.6, 0.2)
        
        # Vertical lines
        for x in range(0, int(self.width), 50):
            Line(points=[x + self.grid_offset, 0, x + self.grid_offset, self.height], width=1)
        
        # Horizontal lines
        for y in range(0, int(self.height), 50):
            Line(points=[0, y + self.grid_offset, self.width, y + self.grid_offset], width=1)
    
    def draw_particles(self):
        """Draw floating particles"""
        for particle in self.particles:
            Color(0, 0.8, 1, particle['alpha'])
            d = particle['size']
            Ellipse(pos=(particle['x'] - d/2, particle['y'] - d/2), size=(d, d))
    
    def draw_pulse(self):
        """Draw central pulse effect"""
        center_x = self.width / 2
        center_y = self.height / 2
        
        # Multiple pulse rings
        for i in range(3):
            alpha = 0.3 * (1 - i * 0.1) * (1 + 0.5 * math.sin(self.pulse_angle + i))
            Color(0, 0.8, 1, alpha)
            radius = 100 + i * 50 + 20 * math.sin(self.pulse_angle + i)
            Line(circle=(center_x, center_y, radius), width=2)
    
    def animate(self, dt):
        """Animate background elements"""
        # Update particles
        for particle in self.particles:
            particle['x'] += particle['speed'] * math.cos(particle['angle'])
            particle['y'] += particle['speed'] * math.sin(particle['angle'])
            
            # Wrap around screen
            if particle['x'] < 0:
                particle['x'] = self.width
            elif particle['x'] > self.width:
                particle['x'] = 0
            
            if particle['y'] < 0:
                particle['y'] = self.height
            elif particle['y'] > self.height:
                particle['y'] = 0
        
        # Update grid
        self.grid_offset += 0.5
        if self.grid_offset > 50:
            self.grid_offset = 0
        
        # Update pulse
        self.pulse_angle += 0.1
        
        # Redraw
        self.canvas.clear()
        self.draw_background()

class LoadingScreen(FloatLayout):
    """JARVIS boot sequence loading screen"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Boot text
        self.boot_label = Label(
            text="JARVIS SYSTEMS BOOTING...",
            font_size=dp(24),
            color=(0, 0.8, 1, 1),
            pos_hint={'center_x': 0.5, 'center_y': 0.6}
        )
        self.add_widget(self.boot_label)
        
        # Status text
        self.status_label = Label(
            text="Initializing AI Core...",
            font_size=dp(16),
            color=(0.5, 0.5, 0.8, 1),
            pos_hint={'center_x': 0.5, 'center_y': 0.4}
        )
        self.add_widget(self.status_label)
        
        # Loading animation
        self.loading_widget = Widget(
            size_hint=(None, None),
            size=(dp(100), dp(100)),
            pos_hint={'center_x': 0.5, 'center_y': 0.3}
        )
        self.add_widget(self.loading_widget)
        
        # Animation state
        self.rotation_angle = 0
        self.boot_step = 0
        
        self.bind(size=self.update_loading_graphics)
    
    def start_boot_animation(self):
        """Start the boot sequence animation"""
        Clock.schedule_interval(self.animate_loading, 1/30)
        Clock.schedule_interval(self.update_boot_text, 0.8)
    
    def animate_loading(self, dt):
        """Animate loading spinner"""
        self.rotation_angle += 5
        self.update_loading_graphics()
    
    def update_loading_graphics(self, *args):
        """Update loading graphics"""
        self.loading_widget.canvas.clear()
        
        with self.loading_widget.canvas:
            PushMatrix()
            
            # Center rotation
            center_x = self.loading_widget.width / 2
            center_y = self.loading_widget.height / 2
            
            Rotate(angle=self.rotation_angle, origin=(center_x, center_y))
            
            # Draw spinning arcs
            Color(0, 0.8, 1, 0.8)
            for i in range(3):
                start_angle = i * 120 + self.rotation_angle
                Line(circle=(center_x, center_y, 30 + i * 10, start_angle, start_angle + 60), width=3)
            
            PopMatrix()
    
    def update_boot_text(self, dt):
        """Update boot sequence text"""
        boot_messages = [
            "Initializing AI Core...",
            "Loading Neural Networks...",
            "Connecting to Gemini API...",
            "Calibrating Voice Systems...",
            "Systems Online"
        ]
        
        if self.boot_step < len(boot_messages):
            self.status_label.text = boot_messages[self.boot_step]
            self.boot_step += 1
        else:
            Clock.unschedule(self.update_boot_text)

class ChatBubble(BoxLayout):
    """Chat message bubble with styling"""
    
    def __init__(self, sender, message, msg_type, **kwargs):
        super().__init__(**kwargs)
        
        self.orientation = 'horizontal'
        self.size_hint_y = None
        self.height = dp(60)
        self.spacing = dp(10)
        self.padding = dp(10)
        
        # Determine bubble style
        if msg_type == "user":
            bg_color = (0.2, 0.2, 0.4, 0.8)
            text_color = (1, 1, 1, 1)
            icon = "👤"
            align = "right"
        elif msg_type == "assistant":
            bg_color = (0, 0.3, 0.6, 0.8)
            text_color = (1, 1, 1, 1)
            icon = "🤖"
            align = "left"
        else:  # system
            bg_color = (0.3, 0.3, 0.3, 0.6)
            text_color = (0.8, 0.8, 0.8, 1)
            icon = "⚙️"
            align = "center"
        
        # Create bubble background
        with self.canvas.before:
            Color(*bg_color)
            self.bg_rect = Rectangle(pos=self.pos, size=self.size)
        
        self.bind(pos=self.update_bg, size=self.update_bg)
        
        # Icon
        icon_label = Label(
            text=icon,
            font_size=dp(20),
            size_hint_x=None,
            width=dp(40),
            color=text_color
        )
        
        # Message content
        content_layout = BoxLayout(orientation='vertical', spacing=dp(2))
        
        sender_label = Label(
            text=sender,
            font_size=dp(12),
            color=(0.8, 0.8, 0.8, 1),
            size_hint_y=None,
            height=dp(20),
            halign=align,
            text_size=(None, None)
        )
        
        message_label = Label(
            text=message,
            font_size=dp(14),
            color=text_color,
            text_size=(None, None),
            halign=align,
            valign='middle'
        )
        
        content_layout.add_widget(sender_label)
        content_layout.add_widget(message_label)
        
        if align == "right":
            self.add_widget(content_layout)
            self.add_widget(icon_label)
        else:
            self.add_widget(icon_label)
            self.add_widget(content_layout)
    
    def update_bg(self, *args):
        """Update background rectangle"""
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size

class MicButton(Button):
    """Animated microphone button"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        self.text = "🎤"
        self.font_size = dp(30)
        self.background_color = (0, 0.8, 1, 1)
        self.background_normal = ''
        
        # Animation state
        self.is_listening = False
        self.pulse_scale = 1.0
        self.ripple_radius = 0
        
        self.bind(size=self.update_graphics)
    
    def start_listening_animation(self):
        """Start listening animation"""
        self.is_listening = True
        self.text = "🔴"
        self.background_color = (1, 0.2, 0.2, 1)
        
        # Start pulse animation
        Clock.schedule_interval(self.animate_listening, 1/30)
    
    def stop_listening_animation(self):
        """Stop listening animation"""
        self.is_listening = False
        self.text = "🎤"
        self.background_color = (0, 0.8, 1, 1)
        
        # Stop animation
        Clock.unschedule(self.animate_listening)
        self.pulse_scale = 1.0
        self.ripple_radius = 0
    
    def animate_listening(self, dt):
        """Animate listening state"""
        if self.is_listening:
            # Pulse effect
            self.pulse_scale = 1.0 + 0.2 * math.sin(Clock.get_time() * 8)
            
            # Ripple effect
            self.ripple_radius += 2
            if self.ripple_radius > 50:
                self.ripple_radius = 0
            
            self.update_graphics()
    
    def update_graphics(self, *args):
        """Update button graphics"""
        if self.is_listening:
            self.canvas.after.clear()
            
            with self.canvas.after:
                # Ripple effect
                Color(1, 0.2, 0.2, 0.3)
                center_x = self.center_x
                center_y = self.center_y
                Line(circle=(center_x, center_y, self.ripple_radius), width=2)
                
                # Pulse glow
                Color(1, 0.2, 0.2, 0.1)
                glow_size = min(self.width, self.height) * self.pulse_scale
                Ellipse(
                    pos=(center_x - glow_size/2, center_y - glow_size/2),
                    size=(glow_size, glow_size)
                )

class WaveformDisplay(Widget):
    """Real-time waveform visualization"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        self.is_active = False
        self.wave_data = [0] * 50
        self.wave_offset = 0
        
        self.bind(size=self.update_waveform)
        Clock.schedule_interval(self.animate_waveform, 1/20)
    
    def set_active(self, active):
        """Set waveform active state"""
        self.is_active = active
    
    def update_waveform(self, *args):
        """Update waveform display"""
        self.canvas.clear()
        
        if self.is_active:
            with self.canvas:
                Color(0, 0.8, 1, 0.8)
                
                # Draw waveform bars
                bar_width = self.width / len(self.wave_data)
                for i, amplitude in enumerate(self.wave_data):
                    x = i * bar_width
                    height = self.height * amplitude * 0.8
                    y = self.center_y - height / 2
                    
                    Rectangle(pos=(x, y), size=(bar_width * 0.8, height))
    
    def animate_waveform(self, dt):
        """Animate waveform"""
        if self.is_active:
            # Generate random waveform data
            for i in range(len(self.wave_data)):
                self.wave_data[i] = random.uniform(0.1, 1.0) * math.sin(self.wave_offset + i * 0.3)
            
            self.wave_offset += 0.2
            self.update_waveform()
        else:
            # Flat line when inactive
            self.wave_data = [0.1] * len(self.wave_data)
            self.update_waveform()
