# Personal Automation Assistant 🤖

A friendly, human-like Python automation assistant that can perform various tasks and engage in natural conversations. Think of it as your personal mini-Jarvis!

## ✨ Features

### 🗣️ Natural Conversation
- Human-like, friendly interaction style
- Asks for confirmation before executing tasks
- Supports both text and voice input/output
- Remembers your name for personalized interaction

### 🔧 Automation Capabilities
- **Email Management**: Send emails with custom recipients, subjects, and messages
- **Reminders**: Set time-based reminders with natural language input
- **Browser Control**: Open websites and perform web searches
- **File Operations**: Rename files, list directories, create folders
- **Voice Interaction**: Optional speech recognition and text-to-speech

### 📊 Advanced Features
- **Comprehensive Logging**: Tracks all interactions and commands
- **Modular Design**: Easy to extend with new automation features
- **Error Handling**: Graceful error recovery and user feedback
- **Session Management**: Maintains conversation context and history

## 🚀 Quick Start

### Installation

1. **Clone or download the files** to your desired directory

2. **Install required dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **For voice capabilities** (optional but recommended):
   ```bash
   # On Windows
   pip install pyaudio
   
   # On macOS
   brew install portaudio
   pip install pyaudio
   
   # On Linux (Ubuntu/Debian)
   sudo apt-get install python3-pyaudio
   pip install pyaudio
   ```

### Running the Assistant

```bash
python assistant.py
```

The assistant will ask if you want to enable voice interaction (if available) and then start the conversation.

## 💬 Usage Examples

### Basic Interaction
```
🤖 Assistant: Hello! I'm your personal assistant. What would you like me to help you with today?
👤 What should I call you? John
🤖 Assistant: Nice to meet you, John! I'm excited to help you today.

👤 What would you like me to do? Send an email to my boss
🤖 Assistant: I can help you send an email! Let me get the details.
👤 Who would you like to send the email to? <EMAIL>
👤 What's the subject? Weekly Report
👤 What would you like to say? Please find attached this week's report.
🤖 Assistant: I'll send an <NAME_EMAIL> with the subject 'Weekly Report'. Shall I go ahead with that?
👤 yes
🤖 Assistant: Great! Email sent <NAME_EMAIL>!
```

### Setting Reminders
```
👤 Remind me to call mom in 2 hours
🤖 Assistant: I'll remind you about 'call mom' in 2 hours. Should I proceed with this task?
👤 yes
🤖 Assistant: Reminder set for 2024-01-15 16:30: 'call mom'
```

### File Operations
```
👤 Rename a file
🤖 Assistant: I can help you with file operations!
👤 What would you like to do? rename
👤 What's the file path? /path/to/old_report.txt
👤 What should the new name be? new_report.txt
🤖 Assistant: I'll rename '/path/to/old_report.txt' to 'new_report.txt'. Ready to execute - should I continue?
👤 yes
🤖 Assistant: Successfully renamed '/path/to/old_report.txt' to 'new_report.txt'
```

## ⚙️ Configuration

### Email Setup
Before using email features, you'll need to configure your email settings:

1. **For Gmail users**:
   - Enable 2-Factor Authentication
   - Generate an App Password: Google Account > Security > 2-Step Verification > App passwords
   - Use the app password instead of your regular password

2. **Run the email setup**:
   ```python
   from automation import EmailAutomation
   email_auto = EmailAutomation()
   email_auto.setup_email_config()
   ```

### Voice Configuration
The assistant will automatically detect and configure available voice capabilities. You can:
- Test voice functionality: Run `python voice_handler.py`
- List available voices and change settings through the assistant interface

## 📁 Project Structure

```
personal-assistant/
├── assistant.py          # Main assistant application
├── automation.py         # Automation modules (email, reminders, etc.)
├── voice_handler.py      # Voice input/output handling
├── logger_config.py      # Logging configuration and utilities
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── logs/                # Generated log files
│   ├── assistant.log    # Main application log
│   ├── errors.log       # Error-specific log
│   └── actions.jsonl    # Detailed action history
└── email_config.json   # Email configuration (created after setup)
```

## 🔧 Extending the Assistant

The assistant is designed to be easily extensible. Here's how to add new features:

### Adding New Commands

1. **Add the command to the automation.py file**:
   ```python
   class NewAutomation:
       def new_feature(self, parameter: str) -> Dict[str, Any]:
           try:
               # Your automation logic here
               return {"success": True, "message": "Feature executed successfully!"}
           except Exception as e:
               return {"success": False, "message": f"Error: {str(e)}"}
   ```

2. **Update the assistant.py file**:
   ```python
   # In __init__ method
   self.new_automation = NewAutomation()
   
   # Add command mapping
   self.commands['newcommand'] = self._handle_new_command
   
   # Add handler method
   def _handle_new_command(self, user_input: str):
       # Get parameters from user
       param = self._get_input("Enter parameter: ")
       
       self.pending_action = {
           'type': 'new_feature',
           'data': {'parameter': param}
       }
       
       self._say(f"I'll execute the new feature with '{param}'. Should I proceed?")
   ```

### Adding New Voice Commands

Voice commands are automatically recognized through natural language processing. The assistant looks for keywords in user input to determine the appropriate action.

## 🐛 Troubleshooting

### Common Issues

1. **Voice not working**:
   - Ensure microphone permissions are granted
   - Install pyaudio: `pip install pyaudio`
   - Test with: `python voice_handler.py`

2. **Email not sending**:
   - Check email configuration in `email_config.json`
   - For Gmail, use App Password instead of regular password
   - Ensure "Less secure app access" is enabled (if not using App Password)

3. **Import errors**:
   - Install all requirements: `pip install -r requirements.txt`
   - Check Python version compatibility (Python 3.7+)

### Logging and Debugging

The assistant maintains comprehensive logs in the `logs/` directory:
- `assistant.log`: General application logs
- `errors.log`: Error-specific logs
- `actions.jsonl`: Detailed interaction history

View recent logs:
```bash
tail -f logs/assistant.log
```

## 🤝 Contributing

Feel free to extend this assistant with new features! Some ideas:
- Calendar integration
- Weather information
- Smart home device control
- Task management and to-do lists
- Integration with external APIs
- GUI interface
- Mobile app companion

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Built with Python 3
- Uses Google Speech Recognition API
- Text-to-speech powered by pyttsx3
- Email functionality via Python's smtplib

---

**Enjoy your new personal automation assistant! 🚀**

For questions or issues, check the logs directory or review the troubleshooting section above.
