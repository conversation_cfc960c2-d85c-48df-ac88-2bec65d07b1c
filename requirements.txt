# Personal Automation Assistant - Required Libraries
# Core dependencies for basic functionality
schedule>=1.2.0

# Email functionality
# Note: smtplib is part of Python standard library

# Voice capabilities (optional but recommended)
speechrecognition>=3.10.0
pyttsx3>=2.90

# Audio processing for speech recognition
pyaudio>=0.2.11

# Alternative speech recognition engines (optional)
pocketsphinx>=0.1.15

# Web browser automation
# Note: webbrowser is part of Python standard library

# File operations
# Note: os, shutil are part of Python standard library

# Logging and JSON handling
# Note: logging, json are part of Python standard library

# Date and time handling
# Note: datetime is part of Python standard library

# Regular expressions
# Note: re is part of Python standard library

# Threading support
# Note: threading is part of Python standard library

# Optional: Enhanced features
requests>=2.28.0  # For future web API integrations
colorama>=0.4.6   # For colored console output
rich>=13.0.0      # For enhanced terminal formatting

# Development and testing (optional)
pytest>=7.0.0     # For running tests
black>=22.0.0     # For code formatting
flake8>=5.0.0     # For code linting
