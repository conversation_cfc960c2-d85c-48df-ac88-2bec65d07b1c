# Personal Automation Assistant - Required Libraries
# Core dependencies for basic functionality
schedule>=1.2.0

# Email functionality
# Note: smtplib is part of Python standard library

# Voice capabilities (required for GUI)
speechrecognition>=3.10.0
pyttsx3>=2.90

# Audio processing for speech recognition
pyaudio>=0.2.11

# Alternative speech recognition engines (optional)
pocketsphinx>=0.1.15

# GUI Dependencies for Jarvis Interface
customtkinter>=5.2.0  # Modern GUI framework
pillow>=10.0.0        # Image processing for GUI
numpy>=1.24.0         # For waveform animations
requests>=2.28.0      # For Gemini API calls

# Web browser automation
# Note: webbrowser is part of Python standard library

# File operations
# Note: os, shutil are part of Python standard library

# Logging and JSON handling
# Note: logging, json are part of Python standard library

# Date and time handling
# Note: datetime is part of Python standard library

# Regular expressions
# Note: re is part of Python standard library

# Threading support
# Note: threading is part of Python standard library

# Optional: Enhanced features
colorama>=0.4.6   # For colored console output
rich>=13.0.0      # For enhanced terminal formatting

# Mobile GUI Alternative (optional)
kivy>=2.2.0       # For mobile-compatible GUI
kivymd>=1.1.1     # Material Design for Kivy

# Development and testing (optional)
pytest>=7.0.0     # For running tests
black>=22.0.0     # For code formatting
flake8>=5.0.0     # For code linting
