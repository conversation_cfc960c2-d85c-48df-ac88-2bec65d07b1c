#!/usr/bin/env python3
"""
Jarvis Mobile GUI - Kivy version for Android/iOS compatibility
A futuristic, touch-friendly interface for mobile devices
"""

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
from kivy.graphics import Color, Ellipse, Line, Rectangle
from kivy.animation import Animation
from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.theming import ThemableBehavior
import threading
import time
import math
import requests
import json
import speech_recognition as sr
import pyttsx3
from datetime import datetime

class AnimatedAvatar(FloatLayout):
    """Animated avatar widget"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.pulse_angle = 0
        self.bind(size=self.update_graphics)
        Clock.schedule_interval(self.animate, 1/30)  # 30 FPS
    
    def update_graphics(self, *args):
        """Update graphics when size changes"""
        self.canvas.clear()
        with self.canvas:
            Color(0, 0.83, 1, 0.3)  # Light blue glow
            d = min(self.width, self.height) * 0.8
            Ellipse(pos=(self.center_x - d/2, self.center_y - d/2), size=(d, d))
    
    def animate(self, dt):
        """Animate the avatar"""
        self.pulse_angle += 0.1
        self.canvas.clear()
        
        with self.canvas:
            # Pulsing effect
            pulse = 1 + 0.1 * math.sin(self.pulse_angle)
            Color(0, 0.83, 1, 0.5)
            d = min(self.width, self.height) * 0.6 * pulse
            Ellipse(pos=(self.center_x - d/2, self.center_y - d/2), size=(d, d))
            
            # Inner circle
            Color(0, 0.83, 1, 1)
            d_inner = d * 0.7
            Line(circle=(self.center_x, self.center_y, d_inner/2), width=2)

class VoiceWaveform(FloatLayout):
    """Voice waveform visualization"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.wave_offset = 0
        self.is_active = False
        self.bind(size=self.update_graphics)
        Clock.schedule_interval(self.animate, 1/20)  # 20 FPS
    
    def set_active(self, active):
        """Set waveform active state"""
        self.is_active = active
    
    def animate(self, dt):
        """Animate the waveform"""
        self.wave_offset += 5
        self.canvas.clear()
        
        if self.is_active:
            with self.canvas:
                Color(0, 0.83, 1, 0.8)
                # Draw animated bars
                bar_width = self.width / 20
                for i in range(20):
                    x = i * bar_width
                    height = self.height * 0.3 * (1 + 0.5 * math.sin((i + self.wave_offset) * 0.3))
                    Rectangle(pos=(x, self.center_y - height/2), size=(bar_width * 0.8, height))

class JarvisMobileApp(MDApp):
    """Main Kivy application"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.theme_cls.theme_style = "Dark"
        self.theme_cls.primary_palette = "LightBlue"
        self.conversation_history = []
        self.is_listening = False
        self.setup_voice()
    
    def setup_voice(self):
        """Initialize voice components"""
        try:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 180)
            
            # Adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
        except Exception as e:
            print(f"Voice setup error: {e}")
    
    def build(self):
        """Build the main interface"""
        self.screen = MDScreen()
        
        # Main layout
        main_layout = MDBoxLayout(
            orientation="vertical",
            spacing="10dp",
            adaptive_height=True,
            padding="20dp"
        )
        
        # Header
        self.create_header(main_layout)
        
        # Avatar section
        self.create_avatar_section(main_layout)
        
        # Chat section
        self.create_chat_section(main_layout)
        
        # Controls
        self.create_controls(main_layout)
        
        self.screen.add_widget(main_layout)
        return self.screen
    
    def create_header(self, parent):
        """Create header with title"""
        header_card = MDCard(
            size_hint_y=None,
            height="80dp",
            elevation=3,
            md_bg_color=(0.1, 0.1, 0.1, 1)
        )
        
        header_layout = MDBoxLayout(
            orientation="horizontal",
            adaptive_height=True,
            padding="20dp"
        )
        
        title = MDLabel(
            text="J.A.R.V.I.S",
            theme_text_color="Custom",
            text_color=(0, 0.83, 1, 1),
            font_style="H4",
            bold=True,
            size_hint_x=0.7
        )
        
        subtitle = MDLabel(
            text="AI Assistant",
            theme_text_color="Secondary",
            font_style="Caption",
            size_hint_x=0.3
        )
        
        header_layout.add_widget(title)
        header_layout.add_widget(subtitle)
        header_card.add_widget(header_layout)
        parent.add_widget(header_card)
    
    def create_avatar_section(self, parent):
        """Create animated avatar section"""
        avatar_card = MDCard(
            size_hint_y=None,
            height="200dp",
            elevation=2,
            md_bg_color=(0.05, 0.05, 0.05, 1)
        )
        
        avatar_layout = MDBoxLayout(
            orientation="horizontal",
            padding="20dp"
        )
        
        # Avatar animation
        self.avatar = AnimatedAvatar(size_hint_x=0.6)
        
        # Status and waveform
        status_layout = MDBoxLayout(
            orientation="vertical",
            size_hint_x=0.4,
            spacing="10dp"
        )
        
        self.status_label = MDLabel(
            text="Ready",
            theme_text_color="Primary",
            font_style="Subtitle1",
            halign="center"
        )
        
        self.waveform = VoiceWaveform(size_hint_y=0.4)
        
        status_layout.add_widget(self.status_label)
        status_layout.add_widget(self.waveform)
        
        avatar_layout.add_widget(self.avatar)
        avatar_layout.add_widget(status_layout)
        avatar_card.add_widget(avatar_layout)
        parent.add_widget(avatar_card)
    
    def create_chat_section(self, parent):
        """Create chat conversation area"""
        chat_card = MDCard(
            size_hint_y=0.5,
            elevation=2,
            md_bg_color=(0.05, 0.05, 0.05, 1)
        )
        
        chat_layout = MDBoxLayout(
            orientation="vertical",
            padding="10dp"
        )
        
        chat_header = MDLabel(
            text="Conversation",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height="40dp"
        )
        
        # Scrollable chat area
        scroll = MDScrollView()
        self.chat_content = MDBoxLayout(
            orientation="vertical",
            adaptive_height=True,
            spacing="5dp"
        )
        
        scroll.add_widget(self.chat_content)
        
        chat_layout.add_widget(chat_header)
        chat_layout.add_widget(scroll)
        chat_card.add_widget(chat_layout)
        parent.add_widget(chat_card)
        
        # Add welcome message
        self.add_message("JARVIS", "Hello! I'm JARVIS, your AI assistant. How can I help you today?", "assistant")
    
    def create_controls(self, parent):
        """Create control buttons and input"""
        controls_card = MDCard(
            size_hint_y=None,
            height="120dp",
            elevation=3,
            md_bg_color=(0.1, 0.1, 0.1, 1)
        )
        
        controls_layout = MDBoxLayout(
            orientation="vertical",
            padding="10dp",
            spacing="10dp"
        )
        
        # Microphone button
        mic_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=0.6,
            spacing="20dp"
        )
        
        self.mic_button = MDIconButton(
            icon="microphone",
            theme_icon_color="Custom",
            icon_color=(0, 0.83, 1, 1),
            icon_size="40dp",
            on_release=self.toggle_listening
        )
        
        self.mic_status = MDLabel(
            text="Tap to speak",
            theme_text_color="Secondary",
            font_style="Caption",
            halign="center"
        )
        
        mic_layout.add_widget(self.mic_button)
        mic_layout.add_widget(self.mic_status)
        
        # Text input
        input_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=0.4,
            spacing="10dp"
        )
        
        self.text_input = MDTextField(
            hint_text="Type your message...",
            multiline=False,
            size_hint_x=0.8
        )
        self.text_input.bind(on_text_validate=self.send_text_message)
        
        send_button = MDRaisedButton(
            text="Send",
            size_hint_x=0.2,
            on_release=self.send_text_message
        )
        
        input_layout.add_widget(self.text_input)
        input_layout.add_widget(send_button)
        
        controls_layout.add_widget(mic_layout)
        controls_layout.add_widget(input_layout)
        controls_card.add_widget(controls_layout)
        parent.add_widget(controls_card)
    
    def add_message(self, sender, message, msg_type="user"):
        """Add message to chat"""
        message_card = MDCard(
            size_hint_y=None,
            height="60dp",
            elevation=1,
            md_bg_color=(0.2, 0.2, 0.2, 1) if msg_type == "user" else (0, 0.3, 0.5, 1),
            padding="10dp"
        )
        
        message_layout = MDBoxLayout(
            orientation="vertical",
            adaptive_height=True
        )
        
        sender_label = MDLabel(
            text=f"{sender}:",
            theme_text_color="Primary",
            font_style="Caption",
            size_hint_y=None,
            height="20dp"
        )
        
        message_label = MDLabel(
            text=message,
            theme_text_color="Primary",
            font_style="Body2",
            text_size=(None, None),
            halign="left"
        )
        
        message_layout.add_widget(sender_label)
        message_layout.add_widget(message_label)
        message_card.add_widget(message_layout)
        
        self.chat_content.add_widget(message_card)
        
        # Store in history
        self.conversation_history.append({
            'sender': sender,
            'message': message,
            'type': msg_type,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        })
    
    def toggle_listening(self, *args):
        """Toggle voice listening"""
        if not self.is_listening:
            self.start_listening()
        else:
            self.stop_listening()
    
    def start_listening(self):
        """Start voice recognition"""
        self.is_listening = True
        self.mic_button.icon = "stop"
        self.mic_button.icon_color = (1, 0.2, 0.2, 1)
        self.mic_status.text = "Listening..."
        self.status_label.text = "Listening..."
        self.waveform.set_active(True)
        
        # Start listening in thread
        threading.Thread(target=self.listen_for_voice, daemon=True).start()
    
    def stop_listening(self):
        """Stop voice recognition"""
        self.is_listening = False
        self.mic_button.icon = "microphone"
        self.mic_button.icon_color = (0, 0.83, 1, 1)
        self.mic_status.text = "Tap to speak"
        self.status_label.text = "Ready"
        self.waveform.set_active(False)
    
    def listen_for_voice(self):
        """Voice recognition in thread"""
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=10)
            
            Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', 'Processing...'))
            text = self.recognizer.recognize_google(audio)
            
            # Add user message
            Clock.schedule_once(lambda dt: self.add_message("You", text, "user"))
            
            # Process with AI
            Clock.schedule_once(lambda dt: self.process_with_ai(text))
            
        except sr.WaitTimeoutError:
            Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', 'No speech detected'))
        except sr.UnknownValueError:
            Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', 'Could not understand'))
        except Exception as e:
            Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', f'Error: {str(e)}'))
        finally:
            Clock.schedule_once(lambda dt: self.stop_listening())
    
    def send_text_message(self, *args):
        """Send text message"""
        message = self.text_input.text.strip()
        if message:
            self.add_message("You", message, "user")
            self.text_input.text = ""
            self.process_with_ai(message)
    
    def process_with_ai(self, message):
        """Process message with AI (placeholder for Gemini API)"""
        def process():
            try:
                Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', 'Thinking...'))
                
                # TODO: Replace with actual Gemini API call
                # For now, use a simple response
                response = f"I understand you said: '{message}'. This is a demo response from JARVIS mobile interface."
                
                # Add AI response
                Clock.schedule_once(lambda dt: self.add_message("JARVIS", response, "assistant"))
                
                # Speak response
                Clock.schedule_once(lambda dt: self.speak_response(response))
                
            except Exception as e:
                error_msg = f"Error processing request: {str(e)}"
                Clock.schedule_once(lambda dt: self.add_message("JARVIS", error_msg, "assistant"))
            finally:
                Clock.schedule_once(lambda dt: setattr(self.status_label, 'text', 'Ready'))
        
        threading.Thread(target=process, daemon=True).start()
    
    def speak_response(self, text):
        """Speak AI response"""
        def speak():
            try:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            except Exception as e:
                print(f"TTS Error: {e}")
        
        threading.Thread(target=speak, daemon=True).start()

def main():
    """Main entry point for mobile app"""
    JarvisMobileApp().run()

if __name__ == "__main__":
    main()
