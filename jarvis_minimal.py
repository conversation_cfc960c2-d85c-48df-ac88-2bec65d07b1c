#!/usr/bin/env python3
"""
JARVIS Minimal - Working version with essential features only
"""

import json
import os
import sys
import time
from datetime import datetime

class MinimalAI:
    """Minimal AI with basic responses"""
    
    def __init__(self):
        self.responses = {
            'hello': "Hello! I'm <PERSON>AR<PERSON><PERSON>, your AI assistant. How can I help you today?",
            'help': "I can help with basic conversation, information, and simple tasks.",
            'time': f"The current time is {datetime.now().strftime('%H:%M:%S')}",
            'date': f"Today's date is {datetime.now().strftime('%Y-%m-%d')}",
            'goodbye': "Goodbye! It was a pleasure assisting you today."
        }
    
    def get_response(self, message):
        """Get AI response"""
        message_lower = message.lower().strip()
        
        # Check for exact matches
        for key, response in self.responses.items():
            if key in message_lower:
                return response
        
        # Default response
        return f"I understand you said: '{message}'. I'm currently in minimal mode for testing."

class JarvisMinimal:
    """Minimal JARVIS implementation"""
    
    def __init__(self):
        self.ai = MinimalAI()
        self.running = True
        self.conversation_count = 0
    
    def start(self):
        """Start JARVIS minimal"""
        self.show_banner()
        self.show_boot_sequence()
        self.main_loop()
    
    def show_banner(self):
        """Show JARVIS banner"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        ██╗ █████╗ ██████╗ ██╗   ██╗██╗███████╗               ║
║        ██║██╔══██╗██╔══██╗██║   ██║██║██╔════╝               ║
║        ██║███████║██████╔╝██║   ██║██║███████╗               ║
║   ██   ██║██╔══██║██╔══██╗╚██╗ ██╔╝██║╚════██║               ║
║   ╚█████╔╝██║  ██║██║  ██║ ╚████╔╝ ██║███████║               ║
║    ╚════╝ ╚═╝  ╚═╝╚═╝  ╚═╝  ╚═══╝  ╚═╝╚══════╝               ║
║                                                              ║
║              Just A Rather Very Intelligent System          ║
║                        Minimal Version                      ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def show_boot_sequence(self):
        """Show boot sequence"""
        boot_steps = [
            "JARVIS SYSTEMS BOOTING...",
            "Initializing AI Core...",
            "Loading Response Database...",
            "Calibrating Interface...",
            "Running System Diagnostics...",
            "All Systems Online"
        ]
        
        for step in boot_steps:
            print(f"🔄 {step}")
            time.sleep(0.8)
        
        print("\n✅ JARVIS Minimal is ready!")
        print(f"🕒 System Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n🤖 Good day! I'm JARVIS, your AI assistant.")
        print("💡 Type 'help' for commands or 'quit' to exit.")
    
    def main_loop(self):
        """Main interaction loop"""
        while self.running:
            try:
                print("\n" + "─" * 60)
                user_input = input("👤 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle quit commands
                if user_input.lower() in ['quit', 'exit', 'bye', 'goodbye']:
                    self.quit()
                    break
                
                # Process message
                self.process_message(user_input)
                self.conversation_count += 1
                
            except KeyboardInterrupt:
                self.quit()
                break
            except EOFError:
                self.quit()
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def process_message(self, message):
        """Process user message"""
        print(f"🔄 Processing: {message}")
        
        # Simulate thinking time
        time.sleep(0.5)
        
        # Get AI response
        response = self.ai.get_response(message)
        
        # Display response
        print(f"🤖 JARVIS: {response}")
        
        # Show conversation count
        print(f"📊 Conversation #{self.conversation_count + 1}")
    
    def quit(self):
        """Quit JARVIS"""
        self.running = False
        print(f"\n🤖 JARVIS: Goodbye! We had {self.conversation_count} conversations.")
        print("🔄 Shutting down systems...")
        time.sleep(1)
        print("✅ JARVIS offline. Have a great day!")

def test_basic_functionality():
    """Test basic functionality"""
    print("🧪 Testing Basic Functionality")
    print("=" * 40)
    
    # Test AI
    ai = MinimalAI()
    test_messages = ["hello", "help", "time", "test message"]
    
    for msg in test_messages:
        response = ai.get_response(msg)
        print(f"👤 {msg}")
        print(f"🤖 {response[:50]}...")
        print()
    
    print("✅ Basic functionality test completed")

def check_environment():
    """Check environment"""
    print("🔍 Environment Check")
    print("=" * 30)
    
    print(f"🐍 Python: {sys.version}")
    print(f"💻 Platform: {sys.platform}")
    print(f"📁 Working Directory: {os.getcwd()}")
    print(f"🕒 Current Time: {datetime.now()}")
    
    # Check if config exists
    if os.path.exists('config.json'):
        print("✅ Config file found")
    else:
        print("⚠️ Config file not found (optional)")
    
    print("✅ Environment check completed")

def main():
    """Main entry point"""
    print("🚀 JARVIS Minimal Test")
    print("=" * 50)
    
    # Environment check
    check_environment()
    print()
    
    # Basic functionality test
    test_basic_functionality()
    print()
    
    # Ask user if they want to run interactive mode
    run_interactive = input("Would you like to run interactive JARVIS? (y/n): ").lower().strip()
    
    if run_interactive.startswith('y'):
        print("\n🚀 Starting Interactive JARVIS...")
        jarvis = JarvisMinimal()
        jarvis.start()
    else:
        print("✅ Test completed successfully!")
        print("\n💡 To run interactive mode later:")
        print("   python jarvis_minimal.py")

if __name__ == "__main__":
    main()
