"""
Logging configuration for the Personal Assistant
Provides comprehensive logging for user commands, actions, and system events.
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
import json


def setup_logging(log_level: str = "INFO", log_to_file: bool = True) -> logging.Logger:
    """
    Set up comprehensive logging for the assistant.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: Whether to log to file in addition to console
        
    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_dir = "logs"
    if log_to_file and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure root logger
    logger = logging.getLogger("PersonalAssistant")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)  # Only show warnings and errors in console
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    if log_to_file:
        # Main log file handler (rotating)
        main_log_file = os.path.join(log_dir, "assistant.log")
        file_handler = RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        logger.addHandler(file_handler)
        
        # Error log file handler
        error_log_file = os.path.join(log_dir, "errors.log")
        error_handler = RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)
    
    return logger


class ActionLogger:
    """
    Specialized logger for tracking user actions and assistant responses.
    Maintains a detailed history of interactions for analysis and debugging.
    """
    
    def __init__(self, log_dir: str = "logs"):
        """Initialize the action logger."""
        self.log_dir = log_dir
        self.actions_file = os.path.join(log_dir, "actions.jsonl")
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Ensure log directory exists
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Log session start
        self._log_action("session_start", {"session_id": self.session_id})
    
    def log_user_input(self, user_input: str, input_method: str = "text"):
        """
        Log user input.
        
        Args:
            user_input: The user's input text
            input_method: Method of input (text, voice)
        """
        self._log_action("user_input", {
            "input": user_input,
            "method": input_method,
            "length": len(user_input)
        })
    
    def log_assistant_response(self, response: str, response_type: str = "text"):
        """
        Log assistant response.
        
        Args:
            response: The assistant's response
            response_type: Type of response (text, voice, action)
        """
        self._log_action("assistant_response", {
            "response": response,
            "type": response_type,
            "length": len(response)
        })
    
    def log_command_execution(self, command: str, parameters: dict, result: dict):
        """
        Log command execution details.
        
        Args:
            command: The command that was executed
            parameters: Parameters passed to the command
            result: Result of the command execution
        """
        self._log_action("command_execution", {
            "command": command,
            "parameters": parameters,
            "success": result.get("success", False),
            "message": result.get("message", ""),
            "execution_time": result.get("execution_time")
        })
    
    def log_error(self, error_type: str, error_message: str, context: dict = None):
        """
        Log error occurrences.
        
        Args:
            error_type: Type of error
            error_message: Error message
            context: Additional context information
        """
        self._log_action("error", {
            "error_type": error_type,
            "message": error_message,
            "context": context or {}
        })
    
    def log_system_event(self, event_type: str, details: dict = None):
        """
        Log system events.
        
        Args:
            event_type: Type of system event
            details: Additional event details
        """
        self._log_action("system_event", {
            "event_type": event_type,
            "details": details or {}
        })
    
    def _log_action(self, action_type: str, data: dict):
        """
        Internal method to log actions to file.
        
        Args:
            action_type: Type of action being logged
            data: Action data
        """
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "session_id": self.session_id,
                "action_type": action_type,
                "data": data
            }
            
            # Append to JSONL file
            with open(self.actions_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
                
        except Exception as e:
            # Fallback logging to prevent logging errors from breaking the app
            print(f"Error logging action: {e}")
    
    def get_session_summary(self) -> dict:
        """
        Get a summary of the current session.
        
        Returns:
            Dictionary containing session statistics
        """
        try:
            stats = {
                "session_id": self.session_id,
                "user_inputs": 0,
                "assistant_responses": 0,
                "commands_executed": 0,
                "errors": 0,
                "start_time": None,
                "duration_minutes": 0
            }
            
            if not os.path.exists(self.actions_file):
                return stats
            
            with open(self.actions_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        if entry["session_id"] != self.session_id:
                            continue
                        
                        action_type = entry["action_type"]
                        
                        if action_type == "session_start":
                            stats["start_time"] = entry["timestamp"]
                        elif action_type == "user_input":
                            stats["user_inputs"] += 1
                        elif action_type == "assistant_response":
                            stats["assistant_responses"] += 1
                        elif action_type == "command_execution":
                            stats["commands_executed"] += 1
                        elif action_type == "error":
                            stats["errors"] += 1
                            
                    except json.JSONDecodeError:
                        continue
            
            # Calculate duration
            if stats["start_time"]:
                start = datetime.fromisoformat(stats["start_time"])
                duration = datetime.now() - start
                stats["duration_minutes"] = round(duration.total_seconds() / 60, 2)
            
            return stats
            
        except Exception as e:
            print(f"Error generating session summary: {e}")
            return {"error": str(e)}
    
    def export_session_log(self, output_file: str = None) -> str:
        """
        Export the current session log to a readable format.
        
        Args:
            output_file: Output file path (optional)
            
        Returns:
            Path to the exported file
        """
        if not output_file:
            output_file = os.path.join(self.log_dir, f"session_{self.session_id}.txt")
        
        try:
            with open(output_file, "w", encoding="utf-8") as out_f:
                out_f.write(f"Personal Assistant Session Log\n")
                out_f.write(f"Session ID: {self.session_id}\n")
                out_f.write(f"Generated: {datetime.now().isoformat()}\n")
                out_f.write("=" * 50 + "\n\n")
                
                if not os.path.exists(self.actions_file):
                    out_f.write("No actions logged in this session.\n")
                    return output_file
                
                with open(self.actions_file, "r", encoding="utf-8") as in_f:
                    for line in in_f:
                        try:
                            entry = json.loads(line.strip())
                            if entry["session_id"] != self.session_id:
                                continue
                            
                            timestamp = entry["timestamp"]
                            action_type = entry["action_type"]
                            data = entry["data"]
                            
                            out_f.write(f"[{timestamp}] {action_type.upper()}\n")
                            
                            if action_type == "user_input":
                                out_f.write(f"  User ({data['method']}): {data['input']}\n")
                            elif action_type == "assistant_response":
                                out_f.write(f"  Assistant ({data['type']}): {data['response']}\n")
                            elif action_type == "command_execution":
                                out_f.write(f"  Command: {data['command']}\n")
                                out_f.write(f"  Success: {data['success']}\n")
                                out_f.write(f"  Message: {data['message']}\n")
                            elif action_type == "error":
                                out_f.write(f"  Error: {data['error_type']} - {data['message']}\n")
                            
                            out_f.write("\n")
                            
                        except json.JSONDecodeError:
                            continue
                
                # Add session summary
                summary = self.get_session_summary()
                out_f.write("\n" + "=" * 50 + "\n")
                out_f.write("SESSION SUMMARY\n")
                out_f.write("=" * 50 + "\n")
                for key, value in summary.items():
                    out_f.write(f"{key.replace('_', ' ').title()}: {value}\n")
            
            return output_file
            
        except Exception as e:
            print(f"Error exporting session log: {e}")
            return ""


# Global action logger instance
action_logger = ActionLogger()


def get_action_logger() -> ActionLogger:
    """Get the global action logger instance."""
    return action_logger


# Utility functions for common logging patterns
def log_user_command(command: str, parameters: dict = None):
    """Convenience function to log user commands."""
    action_logger.log_user_input(f"Command: {command}", "command")
    if parameters:
        action_logger.log_system_event("command_parameters", {"parameters": parameters})


def log_automation_result(automation_type: str, result: dict):
    """Convenience function to log automation results."""
    action_logger.log_command_execution(automation_type, {}, result)


if __name__ == "__main__":
    # Test the logging system
    logger = setup_logging()
    action_logger = ActionLogger()
    
    # Test various log types
    logger.info("Testing logging system")
    action_logger.log_user_input("Hello assistant", "text")
    action_logger.log_assistant_response("Hello! How can I help you?", "text")
    action_logger.log_command_execution("test_command", {"param": "value"}, {"success": True, "message": "Test successful"})
    
    print("Logging test completed. Check the logs directory for output files.")
