#!/usr/bin/env python3
"""
Personal Automation Assistant - A human-like AI assistant for automation tasks
Author: Your Assistant
Version: 1.0
"""

import os
import sys
import json
import logging
import random
from datetime import datetime
from typing import Dict, List, Optional, Callable
import re

# Import automation modules (will be created separately)
from automation import EmailAutomation, ReminderAutomation, BrowserAutomation, FileAutomation
from voice_handler import VoiceHandler
from logger_config import setup_logging


class PersonalAssistant:
    """
    A friendly, human-like automation assistant that can perform various tasks
    and engage in natural conversations with users.
    """
    
    def __init__(self, voice_enabled: bool = False):
        """Initialize the assistant with optional voice capabilities."""
        self.voice_enabled = voice_enabled
        self.voice_handler = VoiceHandler() if voice_enabled else None
        self.logger = setup_logging()
        
        # Initialize automation modules
        self.email_automation = EmailAutomation()
        self.reminder_automation = ReminderAutomation()
        self.browser_automation = BrowserAutomation()
        self.file_automation = FileAutomation()
        
        # Conversation state
        self.user_name = None
        self.pending_action = None
        self.context = {}
        
        # Friendly responses for different situations
        self.greetings = [
            "Hello! I'm your personal assistant. What would you like me to help you with today?",
            "Hi there! I'm here to help you with any tasks you need. What can I do for you?",
            "Good to see you! I'm ready to assist you with various tasks. How can I help?",
            "Hey! Your friendly assistant is here. What would you like me to take care of today?"
        ]
        
        self.confirmations = [
            "Shall I go ahead with that?",
            "Should I proceed with this task?",
            "Would you like me to do that now?",
            "Ready to execute - should I continue?"
        ]
        
        self.cancellations = [
            "No problem! Task cancelled. Anything else I can help with?",
            "Understood! I've cancelled that. What else would you like me to do?",
            "Sure thing! Task cancelled. Is there something else I can assist you with?",
            "Got it! I won't do that. What else can I help you with today?"
        ]
        
        # Command mappings
        self.commands = {
            'email': self._handle_email,
            'reminder': self._handle_reminder,
            'remind': self._handle_reminder,
            'browser': self._handle_browser,
            'open': self._handle_browser,
            'file': self._handle_file,
            'rename': self._handle_file,
            'help': self._show_help,
            'quit': self._quit,
            'exit': self._quit,
            'bye': self._quit
        }
    
    def start(self):
        """Start the assistant and begin conversation."""
        self._say(random.choice(self.greetings))
        self._get_user_name()
        
        while True:
            try:
                user_input = self._get_input("\nWhat would you like me to do? ")
                
                if not user_input.strip():
                    self._say("I didn't catch that. Could you please tell me what you'd like me to do?")
                    continue
                
                self._process_input(user_input)
                
            except KeyboardInterrupt:
                self._say("\nGoodbye! Have a great day!")
                break
            except Exception as e:
                self.logger.error(f"Error processing input: {e}")
                self._say("Sorry, I encountered an error. Could you try that again?")
    
    def _get_user_name(self):
        """Get the user's name for personalized interaction."""
        if not self.user_name:
            name = self._get_input("By the way, what should I call you? ")
            self.user_name = name.strip() if name.strip() else "Friend"
            self._say(f"Nice to meet you, {self.user_name}! I'm excited to help you today.")
    
    def _process_input(self, user_input: str):
        """Process user input and determine appropriate action."""
        user_input = user_input.lower().strip()
        
        # Handle yes/no responses for pending actions
        if self.pending_action:
            if self._is_affirmative(user_input):
                self._execute_pending_action()
            elif self._is_negative(user_input):
                self._cancel_pending_action()
            else:
                self._say("I need a yes or no answer. Should I proceed with the task?")
            return
        
        # Parse command from input
        command = self._extract_command(user_input)
        
        if command in self.commands:
            self.commands[command](user_input)
        else:
            self._handle_unknown_command(user_input)
    
    def _extract_command(self, user_input: str) -> str:
        """Extract the main command from user input."""
        # Look for key command words
        for cmd in self.commands.keys():
            if cmd in user_input:
                return cmd
        
        # Check for common patterns
        if any(word in user_input for word in ['send', 'mail', 'email']):
            return 'email'
        elif any(word in user_input for word in ['remind', 'reminder', 'remember']):
            return 'reminder'
        elif any(word in user_input for word in ['open', 'browse', 'website', 'url']):
            return 'browser'
        elif any(word in user_input for word in ['rename', 'file', 'folder']):
            return 'file'
        
        return 'unknown'
    
    def _is_affirmative(self, response: str) -> bool:
        """Check if response is affirmative."""
        affirmative = ['yes', 'y', 'yeah', 'yep', 'sure', 'ok', 'okay', 'go ahead', 'proceed', 'do it']
        return any(word in response for word in affirmative)
    
    def _is_negative(self, response: str) -> bool:
        """Check if response is negative."""
        negative = ['no', 'n', 'nope', 'cancel', 'stop', 'don\'t', 'abort', 'nevermind']
        return any(word in response for word in negative)
    
    def _handle_email(self, user_input: str):
        """Handle email-related commands."""
        self._say("I can help you send an email! Let me get the details.")
        
        recipient = self._get_input("Who would you like to send the email to? ")
        subject = self._get_input("What's the subject? ")
        message = self._get_input("What would you like to say? ")
        
        self.pending_action = {
            'type': 'email',
            'data': {'recipient': recipient, 'subject': subject, 'message': message}
        }
        
        self._say(f"I'll send an email to {recipient} with the subject '{subject}'. {random.choice(self.confirmations)}")
    
    def _handle_reminder(self, user_input: str):
        """Handle reminder-related commands."""
        self._say("I'll help you set a reminder!")
        
        reminder_text = self._get_input("What would you like me to remind you about? ")
        time_input = self._get_input("When should I remind you? (e.g., '5 minutes', '2 hours', 'tomorrow 9am') ")
        
        self.pending_action = {
            'type': 'reminder',
            'data': {'text': reminder_text, 'time': time_input}
        }
        
        self._say(f"I'll remind you about '{reminder_text}' {time_input}. {random.choice(self.confirmations)}")
    
    def _handle_browser(self, user_input: str):
        """Handle browser-related commands."""
        url = self._get_input("What website would you like me to open? ")
        
        self.pending_action = {
            'type': 'browser',
            'data': {'url': url}
        }
        
        self._say(f"I'll open {url} in your browser. {random.choice(self.confirmations)}")
    
    def _handle_file(self, user_input: str):
        """Handle file-related commands."""
        self._say("I can help you with file operations!")
        
        operation = self._get_input("What would you like to do? (rename/move/copy) ")
        file_path = self._get_input("What's the file path? ")
        
        if 'rename' in operation.lower():
            new_name = self._get_input("What should the new name be? ")
            self.pending_action = {
                'type': 'file_rename',
                'data': {'file_path': file_path, 'new_name': new_name}
            }
            self._say(f"I'll rename '{file_path}' to '{new_name}'. {random.choice(self.confirmations)}")
        else:
            self._say("I currently support file renaming. More operations coming soon!")
    
    def _execute_pending_action(self):
        """Execute the pending action."""
        if not self.pending_action:
            return
        
        action_type = self.pending_action['type']
        data = self.pending_action['data']
        
        try:
            if action_type == 'email':
                result = self.email_automation.send_email(**data)
            elif action_type == 'reminder':
                result = self.reminder_automation.set_reminder(**data)
            elif action_type == 'browser':
                result = self.browser_automation.open_url(**data)
            elif action_type == 'file_rename':
                result = self.file_automation.rename_file(**data)
            
            if result['success']:
                self._say(f"Great! {result['message']}")
                self.logger.info(f"Successfully executed {action_type}: {data}")
            else:
                self._say(f"Sorry, I couldn't complete that task. {result['message']}")
                self.logger.error(f"Failed to execute {action_type}: {result['message']}")
                
        except Exception as e:
            self._say("I encountered an error while trying to complete that task.")
            self.logger.error(f"Error executing {action_type}: {e}")
        
        self.pending_action = None
    
    def _cancel_pending_action(self):
        """Cancel the pending action."""
        self.pending_action = None
        self._say(random.choice(self.cancellations))
    
    def _handle_unknown_command(self, user_input: str):
        """Handle unknown commands with helpful suggestions."""
        self._say("I'm not sure how to help with that yet. Here's what I can do:")
        self._show_help("")
    
    def _show_help(self, user_input: str):
        """Show available commands."""
        help_text = """
Here are the things I can help you with:
• Send emails
• Set reminders
• Open websites in your browser
• Rename files
• Have a friendly conversation!

Just tell me what you'd like to do in natural language, like:
- "Send an email to John"
- "Remind me to call mom in 2 hours"
- "Open Google in my browser"
- "Rename the file report.txt"
        """
        self._say(help_text)
    
    def _quit(self, user_input: str):
        """Quit the assistant."""
        self._say(f"Goodbye, {self.user_name}! It was great helping you today. Take care!")
        sys.exit(0)
    
    def _say(self, message: str):
        """Output message via text or voice."""
        print(f"\n🤖 Assistant: {message}")
        
        if self.voice_enabled and self.voice_handler:
            self.voice_handler.speak(message)
    
    def _get_input(self, prompt: str) -> str:
        """Get input via text or voice."""
        if self.voice_enabled and self.voice_handler:
            print(f"\n👤 {prompt}")
            return self.voice_handler.listen()
        else:
            return input(f"\n👤 {prompt}")


def main():
    """Main entry point for the assistant."""
    print("🚀 Personal Automation Assistant Starting...")
    
    # Check if voice is available
    voice_available = False
    try:
        import speech_recognition
        import pyttsx3
        voice_available = True
    except ImportError:
        pass
    
    if voice_available:
        use_voice = input("Would you like to enable voice interaction? (y/n): ").lower().startswith('y')
    else:
        use_voice = False
        print("Voice features not available. Install speech_recognition and pyttsx3 for voice support.")
    
    assistant = PersonalAssistant(voice_enabled=use_voice)
    assistant.start()


if __name__ == "__main__":
    main()
