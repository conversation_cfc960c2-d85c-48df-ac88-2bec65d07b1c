#!/usr/bin/env python3
"""
JARVIS Simple - Basic console version for testing
Works without GUI dependencies
"""

import threading
import time
import json
import os
import queue
from datetime import datetime

class SimpleAI:
    """Simple AI for testing without Gemini API"""
    
    def __init__(self):
        self.conversation_history = []
    
    def initialize(self):
        """Initialize AI"""
        print("🧠 AI Core initialized (Simple mode)")
    
    def get_response(self, message):
        """Get AI response"""
        message_lower = message.lower()
        
        # Simple responses based on keywords
        if any(word in message_lower for word in ['hello', 'hi', 'hey']):
            return "Hello! I'm JAR<PERSON><PERSON>, your AI assistant. How can I help you today?"
        
        elif any(word in message_lower for word in ['how are you', 'how do you do']):
            return "I'm functioning optimally, thank you for asking. All systems are online."
        
        elif any(word in message_lower for word in ['what can you do', 'help', 'capabilities']):
            return """I can help you with:
• Natural conversation
• Email automation (when configured)
• Setting reminders
• Opening websites
• File operations
• And much more!"""
        
        elif any(word in message_lower for word in ['email', 'send email']):
            return "I can help you send emails. Please configure your email settings first."
        
        elif any(word in message_lower for word in ['reminder', 'remind me']):
            return "I'll help you set a reminder. What would you like me to remind you about?"
        
        elif any(word in message_lower for word in ['open', 'website', 'browser']):
            return "I can open websites for you. Which website would you like me to open?"
        
        elif any(word in message_lower for word in ['goodbye', 'bye', 'exit', 'quit']):
            return "Goodbye! It was a pleasure assisting you today."
        
        else:
            return f"I understand you said: '{message}'. I'm currently in simple mode. For full AI capabilities, please configure the Gemini API."

class SimpleVoice:
    """Simple voice handler for testing"""
    
    def __init__(self):
        self.available = False
    
    def initialize(self):
        """Initialize voice"""
        try:
            import speech_recognition as sr
            import pyttsx3
            self.available = True
            print("🎤 Voice systems initialized")
        except ImportError:
            print("🎤 Voice systems not available (install speechrecognition and pyttsx3)")
    
    def listen(self):
        """Listen for voice input"""
        if not self.available:
            return None
        
        try:
            import speech_recognition as sr
            recognizer = sr.Recognizer()
            microphone = sr.Microphone()
            
            print("🎤 Listening...")
            with microphone as source:
                audio = recognizer.listen(source, timeout=5, phrase_time_limit=10)
            
            print("🔄 Processing...")
            text = recognizer.recognize_google(audio)
            print(f"👤 You said: {text}")
            return text
            
        except Exception as e:
            print(f"Voice error: {e}")
            return None
    
    def speak(self, text):
        """Speak text"""
        if not self.available:
            return
        
        try:
            import pyttsx3
            engine = pyttsx3.init()
            engine.say(text)
            engine.runAndWait()
        except Exception as e:
            print(f"TTS error: {e}")

class JarvisSimple:
    """Simple JARVIS console interface"""
    
    def __init__(self):
        self.ai = SimpleAI()
        self.voice = SimpleVoice()
        self.running = True
        self.conversation_history = []
    
    def start(self):
        """Start JARVIS"""
        print("🚀 JARVIS Simple - Starting...")
        print("=" * 50)
        
        # Initialize components
        self.ai.initialize()
        self.voice.initialize()
        
        # Boot sequence
        self.show_boot_sequence()
        
        # Main loop
        self.main_loop()
    
    def show_boot_sequence(self):
        """Show boot sequence"""
        boot_messages = [
            "JARVIS SYSTEMS BOOTING...",
            "Initializing AI Core...",
            "Loading Neural Networks...",
            "Connecting to APIs...",
            "Calibrating Voice Systems...",
            "Systems Online"
        ]
        
        for message in boot_messages:
            print(f"🔄 {message}")
            time.sleep(0.8)
        
        print("\n✅ JARVIS is ready!")
        print("\n🤖 Good day! I'm JARVIS, your AI assistant.")
        print("💡 Type 'voice' to use voice input, 'help' for commands, or 'quit' to exit.")
    
    def main_loop(self):
        """Main interaction loop"""
        while self.running:
            try:
                print("\n" + "─" * 50)
                user_input = input("👤 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() == 'quit' or user_input.lower() == 'exit':
                    self.quit()
                    break
                
                elif user_input.lower() == 'voice':
                    self.handle_voice_input()
                    continue
                
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue
                
                elif user_input.lower() == 'history':
                    self.show_history()
                    continue
                
                # Process with AI
                self.process_message(user_input)
                
            except KeyboardInterrupt:
                self.quit()
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def handle_voice_input(self):
        """Handle voice input"""
        if not self.voice.available:
            print("❌ Voice input not available. Please install speechrecognition and pyttsx3.")
            return
        
        text = self.voice.listen()
        if text:
            self.process_message(text)
        else:
            print("❌ Could not understand voice input")
    
    def process_message(self, message):
        """Process user message"""
        print(f"🔄 Processing: {message}")
        
        # Get AI response
        response = self.ai.get_response(message)
        
        # Display response
        print(f"🤖 JARVIS: {response}")
        
        # Speak response if voice is available
        if self.voice.available:
            threading.Thread(target=self.voice.speak, args=(response,), daemon=True).start()
        
        # Store in history
        self.conversation_history.append({
            'user': message,
            'assistant': response,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        })
    
    def show_help(self):
        """Show help information"""
        help_text = """
🤖 JARVIS Commands:
• Type any message to chat with JARVIS
• 'voice' - Use voice input (if available)
• 'help' - Show this help
• 'history' - Show conversation history
• 'quit' or 'exit' - Exit JARVIS

🎯 Try saying:
• "Hello JARVIS"
• "What can you do?"
• "Send an email"
• "Set a reminder"
• "Open Google"
        """
        print(help_text)
    
    def show_history(self):
        """Show conversation history"""
        if not self.conversation_history:
            print("📝 No conversation history yet.")
            return
        
        print("\n📝 Conversation History:")
        print("=" * 30)
        
        for i, entry in enumerate(self.conversation_history[-5:], 1):  # Last 5 entries
            print(f"{i}. [{entry['timestamp']}]")
            print(f"   👤 You: {entry['user']}")
            print(f"   🤖 JARVIS: {entry['assistant'][:100]}...")
            print()
    
    def quit(self):
        """Quit JARVIS"""
        self.running = False
        response = "Goodbye! It was a pleasure assisting you today. Take care!"
        print(f"\n🤖 JARVIS: {response}")
        
        if self.voice.available:
            self.voice.speak(response)

def test_dependencies():
    """Test available dependencies"""
    print("🧪 Testing Dependencies:")
    print("=" * 30)
    
    # Test basic Python
    print("✅ Python - OK")
    
    # Test requests
    try:
        import requests
        print("✅ Requests - OK")
    except ImportError:
        print("❌ Requests - Missing (pip install requests)")
    
    # Test speech recognition
    try:
        import speech_recognition
        print("✅ Speech Recognition - OK")
    except ImportError:
        print("❌ Speech Recognition - Missing (pip install speechrecognition)")
    
    # Test TTS
    try:
        import pyttsx3
        print("✅ Text-to-Speech - OK")
    except ImportError:
        print("❌ Text-to-Speech - Missing (pip install pyttsx3)")
    
    # Test Kivy
    try:
        import kivy
        print("✅ Kivy GUI - OK")
    except ImportError:
        print("❌ Kivy GUI - Missing (pip install kivy)")
    
    print("\n💡 Install missing dependencies for full functionality")

def main():
    """Main entry point"""
    print("🤖 JARVIS Simple Test Version")
    print("=" * 50)
    
    # Test dependencies
    test_dependencies()
    
    print("\n🚀 Starting JARVIS Simple...")
    
    # Start JARVIS
    jarvis = JarvisSimple()
    jarvis.start()

if __name__ == "__main__":
    main()
