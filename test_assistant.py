#!/usr/bin/env python3
"""
Test script for the Personal Automation Assistant
Tests all major functionality and provides a comprehensive verification suite.
"""

import os
import sys
import json
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from automation import EmailAutomation, ReminderAutomation, BrowserAutomation, FileAutomation
    from voice_handler import VoiceHandler
    from logger_config import setup_logging, ActionLogger
    from assistant import PersonalAssistant
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure all assistant files are in the same directory as this test script.")
    sys.exit(1)


class TestEmailAutomation(unittest.TestCase):
    """Test email automation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.email_automation = EmailAutomation()
    
    def test_email_config_loading(self):
        """Test email configuration loading."""
        config = self.email_automation._load_email_config()
        self.assertIsInstance(config, dict)
        self.assertIn("smtp_server", config)
        self.assertIn("smtp_port", config)
    
    @patch('smtplib.SMTP')
    def test_send_email_success(self, mock_smtp):
        """Test successful email sending."""
        # Mock SMTP server
        mock_server = Mock()
        mock_smtp.return_value = mock_server
        
        # Set up email config
        self.email_automation.config = {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "email": "<EMAIL>",
            "password": "testpass"
        }
        
        result = self.email_automation.send_email(
            "<EMAIL>",
            "Test Subject",
            "Test message"
        )
        
        self.assertTrue(result["success"])
        self.assertIn("sent successfully", result["message"])
    
    def test_send_email_no_config(self):
        """Test email sending without configuration."""
        self.email_automation.config = {"email": "", "password": ""}
        
        result = self.email_automation.send_email(
            "<EMAIL>",
            "Test",
            "Test"
        )
        
        self.assertFalse(result["success"])
        self.assertIn("not configured", result["message"])


class TestReminderAutomation(unittest.TestCase):
    """Test reminder automation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reminder_automation = ReminderAutomation()
    
    def test_parse_time_input_minutes(self):
        """Test parsing minute-based time input."""
        result = self.reminder_automation._parse_time_input("5 minutes")
        expected = datetime.now() + timedelta(minutes=5)
        
        # Allow for small time differences due to execution time
        self.assertLess(abs((result - expected).total_seconds()), 2)
    
    def test_parse_time_input_hours(self):
        """Test parsing hour-based time input."""
        result = self.reminder_automation._parse_time_input("2 hours")
        expected = datetime.now() + timedelta(hours=2)
        
        self.assertLess(abs((result - expected).total_seconds()), 2)
    
    def test_set_reminder(self):
        """Test setting a reminder."""
        result = self.reminder_automation.set_reminder("Test reminder", "5 minutes")
        
        self.assertTrue(result["success"])
        self.assertIn("Reminder set", result["message"])
        self.assertEqual(len(self.reminder_automation.reminders), 1)


class TestBrowserAutomation(unittest.TestCase):
    """Test browser automation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.browser_automation = BrowserAutomation()
    
    @patch('webbrowser.open')
    def test_open_url_with_protocol(self, mock_open):
        """Test opening URL with protocol."""
        result = self.browser_automation.open_url("https://www.google.com")
        
        mock_open.assert_called_once_with("https://www.google.com")
        self.assertTrue(result["success"])
    
    @patch('webbrowser.open')
    def test_open_url_without_protocol(self, mock_open):
        """Test opening URL without protocol."""
        result = self.browser_automation.open_url("google.com")
        
        mock_open.assert_called_once_with("https://google.com")
        self.assertTrue(result["success"])
    
    @patch('webbrowser.open')
    def test_search_google(self, mock_open):
        """Test Google search functionality."""
        result = self.browser_automation.search_google("python programming")
        
        expected_url = "https://www.google.com/search?q=python+programming"
        mock_open.assert_called_once_with(expected_url)
        self.assertTrue(result["success"])


class TestFileAutomation(unittest.TestCase):
    """Test file automation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.file_automation = FileAutomation()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_rename_file_success(self):
        """Test successful file renaming."""
        # Create a test file
        test_file = os.path.join(self.temp_dir, "test_file.txt")
        with open(test_file, "w") as f:
            f.write("test content")
        
        result = self.file_automation.rename_file(test_file, "renamed_file.txt")
        
        self.assertTrue(result["success"])
        self.assertFalse(os.path.exists(test_file))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "renamed_file.txt")))
    
    def test_rename_nonexistent_file(self):
        """Test renaming a non-existent file."""
        result = self.file_automation.rename_file("nonexistent.txt", "new_name.txt")
        
        self.assertFalse(result["success"])
        self.assertIn("not found", result["message"])
    
    def test_list_files(self):
        """Test listing files in directory."""
        # Create test files
        for i in range(3):
            test_file = os.path.join(self.temp_dir, f"test_file_{i}.txt")
            with open(test_file, "w") as f:
                f.write(f"content {i}")
        
        result = self.file_automation.list_files(self.temp_dir)
        
        self.assertTrue(result["success"])
        self.assertEqual(len(result["data"]), 3)
    
    def test_create_directory(self):
        """Test directory creation."""
        new_dir = os.path.join(self.temp_dir, "new_directory")
        result = self.file_automation.create_directory(new_dir)
        
        self.assertTrue(result["success"])
        self.assertTrue(os.path.exists(new_dir))
        self.assertTrue(os.path.isdir(new_dir))


class TestVoiceHandler(unittest.TestCase):
    """Test voice handler functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock the voice libraries to avoid import errors in testing
        with patch.dict('sys.modules', {
            'speech_recognition': Mock(),
            'pyttsx3': Mock()
        }):
            self.voice_handler = VoiceHandler()
    
    def test_voice_initialization(self):
        """Test voice handler initialization."""
        self.assertIsNotNone(self.voice_handler)
    
    def test_clean_text_for_speech(self):
        """Test text cleaning for speech synthesis."""
        test_text = "🤖 Hello! This is a test with emojis 😊 and symbols @#$%"
        cleaned = self.voice_handler._clean_text_for_speech(test_text)
        
        # Should remove emojis and replace symbols
        self.assertNotIn("🤖", cleaned)
        self.assertNotIn("😊", cleaned)
        self.assertIn("at", cleaned)  # @ should be replaced with "at"


class TestActionLogger(unittest.TestCase):
    """Test action logging functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.action_logger = ActionLogger(log_dir=self.temp_dir)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_log_user_input(self):
        """Test logging user input."""
        self.action_logger.log_user_input("test input", "text")
        
        # Check if log file was created
        self.assertTrue(os.path.exists(self.action_logger.actions_file))
    
    def test_session_summary(self):
        """Test session summary generation."""
        # Log some actions
        self.action_logger.log_user_input("test", "text")
        self.action_logger.log_assistant_response("response", "text")
        self.action_logger.log_command_execution("test_cmd", {}, {"success": True})
        
        summary = self.action_logger.get_session_summary()
        
        self.assertEqual(summary["user_inputs"], 1)
        self.assertEqual(summary["assistant_responses"], 1)
        self.assertEqual(summary["commands_executed"], 1)
    
    def test_export_session_log(self):
        """Test session log export."""
        # Log some actions
        self.action_logger.log_user_input("test input", "text")
        self.action_logger.log_assistant_response("test response", "text")
        
        export_file = self.action_logger.export_session_log()
        
        self.assertTrue(os.path.exists(export_file))
        
        # Check file content
        with open(export_file, "r") as f:
            content = f.read()
            self.assertIn("Personal Assistant Session Log", content)
            self.assertIn("test input", content)
            self.assertIn("test response", content)


class TestPersonalAssistant(unittest.TestCase):
    """Test main assistant functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        with patch('builtins.input', return_value='TestUser'):
            self.assistant = PersonalAssistant(voice_enabled=False)
    
    def test_assistant_initialization(self):
        """Test assistant initialization."""
        self.assertIsNotNone(self.assistant)
        self.assertFalse(self.assistant.voice_enabled)
        self.assertIsNotNone(self.assistant.commands)
    
    def test_extract_command(self):
        """Test command extraction from user input."""
        test_cases = [
            ("send an email", "email"),
            ("remind me to call", "reminder"),
            ("open google", "browser"),
            ("rename a file", "file"),
            ("help me", "help"),
            ("unknown command", "unknown")
        ]
        
        for user_input, expected in test_cases:
            result = self.assistant._extract_command(user_input)
            self.assertEqual(result, expected, f"Failed for input: {user_input}")
    
    def test_affirmative_responses(self):
        """Test affirmative response detection."""
        affirmative_responses = ["yes", "y", "yeah", "sure", "ok", "go ahead"]
        
        for response in affirmative_responses:
            self.assertTrue(self.assistant._is_affirmative(response))
    
    def test_negative_responses(self):
        """Test negative response detection."""
        negative_responses = ["no", "n", "nope", "cancel", "stop", "don't"]
        
        for response in negative_responses:
            self.assertTrue(self.assistant._is_negative(response))


def run_integration_tests():
    """Run integration tests that require user interaction."""
    print("\n🧪 Running Integration Tests...")
    print("=" * 50)
    
    # Test logging setup
    print("Testing logging setup...")
    logger = setup_logging()
    logger.info("Test log message")
    print("✅ Logging test passed")
    
    # Test voice handler (if available)
    print("\nTesting voice handler...")
    try:
        voice_handler = VoiceHandler()
        if voice_handler.voice_available:
            print("✅ Voice handler initialized successfully")
            voice_handler.list_available_voices()
        else:
            print("⚠️  Voice capabilities not available")
    except Exception as e:
        print(f"❌ Voice handler test failed: {e}")
    
    # Test automation modules
    print("\nTesting automation modules...")
    
    # Browser automation
    browser_auto = BrowserAutomation()
    print("✅ Browser automation initialized")
    
    # File automation
    file_auto = FileAutomation()
    result = file_auto.list_files(".")
    if result["success"]:
        print("✅ File automation working")
    else:
        print(f"❌ File automation failed: {result['message']}")
    
    # Reminder automation
    reminder_auto = ReminderAutomation()
    print("✅ Reminder automation initialized")
    
    print("\n🎉 Integration tests completed!")


def main():
    """Main test runner."""
    print("🚀 Personal Automation Assistant - Test Suite")
    print("=" * 50)
    
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration tests
    run_integration_tests()
    
    print("\n📊 Test Summary:")
    print("- Unit tests: Check output above")
    print("- Integration tests: Completed")
    print("- Manual testing: Run 'python assistant.py' to test interactively")
    
    print("\n💡 Next Steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Configure email (optional): Run assistant and use email features")
    print("3. Test voice features (optional): Enable voice when starting assistant")
    print("4. Start using your assistant: python assistant.py")


if __name__ == "__main__":
    main()
