#!/usr/bin/env python3
"""
Simple verification script to check if all modules can be imported and basic functionality works.
"""

import sys
import os

def test_imports():
    """Test if all modules can be imported."""
    print("🧪 Testing module imports...")
    
    try:
        print("  • Testing automation module...")
        from automation import EmailAutomation, ReminderAutomation, BrowserAutomation, FileAutomation
        print("    ✅ Automation module imported successfully")
        
        print("  • Testing voice handler...")
        from voice_handler import VoiceHandler
        print("    ✅ Voice handler imported successfully")
        
        print("  • Testing logger config...")
        from logger_config import setup_logging, ActionLogger
        print("    ✅ Logger config imported successfully")
        
        print("  • Testing main assistant...")
        from assistant import PersonalAssistant
        print("    ✅ Main assistant imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"    ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"    ❌ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of each module."""
    print("\n🔧 Testing basic functionality...")
    
    try:
        # Test automation modules
        print("  • Testing email automation...")
        from automation import EmailAutomation
        email_auto = EmailAutomation()
        config = email_auto._load_email_config()
        print(f"    ✅ Email config loaded: {type(config).__name__}")
        
        print("  • Testing reminder automation...")
        from automation import ReminderAutomation
        reminder_auto = ReminderAutomation()
        test_time = reminder_auto._parse_time_input("5 minutes")
        print(f"    ✅ Time parsing works: {test_time}")
        
        print("  • Testing browser automation...")
        from automation import BrowserAutomation
        browser_auto = BrowserAutomation()
        print("    ✅ Browser automation initialized")
        
        print("  • Testing file automation...")
        from automation import FileAutomation
        file_auto = FileAutomation()
        result = file_auto.list_files(".")
        print(f"    ✅ File listing works: {result['success']}")
        
        print("  • Testing logging...")
        from logger_config import setup_logging, ActionLogger
        logger = setup_logging()
        action_logger = ActionLogger()
        print("    ✅ Logging system initialized")
        
        print("  • Testing voice handler...")
        from voice_handler import VoiceHandler
        voice_handler = VoiceHandler()
        print(f"    ✅ Voice handler initialized (available: {voice_handler.voice_available})")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Functionality test error: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available."""
    print("\n📦 Checking dependencies...")
    
    required_modules = [
        ("schedule", "Reminder scheduling"),
        ("json", "JSON handling"),
        ("datetime", "Date/time operations"),
        ("logging", "Logging system"),
        ("webbrowser", "Browser automation"),
        ("smtplib", "Email sending"),
        ("os", "File operations"),
        ("re", "Regular expressions"),
        ("threading", "Background tasks")
    ]
    
    optional_modules = [
        ("speech_recognition", "Voice input"),
        ("pyttsx3", "Text-to-speech"),
        ("pyaudio", "Audio processing")
    ]
    
    all_good = True
    
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module} - {description}")
        except ImportError:
            print(f"  ❌ {module} - {description} (REQUIRED)")
            all_good = False
    
    print("\n  Optional modules:")
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"  ✅ {module} - {description}")
        except ImportError:
            print(f"  ⚠️  {module} - {description} (optional)")
    
    return all_good

def main():
    """Main verification function."""
    print("🚀 Personal Automation Assistant - Verification")
    print("=" * 50)
    
    # Check Python version
    print(f"🐍 Python version: {sys.version}")
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required")
        return False
    
    # Check dependencies
    deps_ok = check_dependencies()
    
    # Test imports
    imports_ok = test_imports()
    
    # Test functionality
    functionality_ok = test_basic_functionality()
    
    # Summary
    print("\n📊 Verification Summary:")
    print(f"  • Dependencies: {'✅' if deps_ok else '❌'}")
    print(f"  • Imports: {'✅' if imports_ok else '❌'}")
    print(f"  • Functionality: {'✅' if functionality_ok else '❌'}")
    
    if deps_ok and imports_ok and functionality_ok:
        print("\n🎉 All checks passed! Your assistant is ready to use.")
        print("\n💡 Next steps:")
        print("  1. Run: python assistant.py")
        print("  2. Try the demo: python demo.py")
        print("  3. Install voice dependencies if needed: pip install speechrecognition pyttsx3 pyaudio")
        return True
    else:
        print("\n⚠️  Some issues found. Please check the errors above.")
        if not deps_ok:
            print("  • Install missing dependencies: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
