#!/usr/bin/env python3
"""
JARVIS Test Suite
Comprehensive testing of all components
"""

import sys
import os
import json
import traceback

def test_python_version():
    """Test Python version"""
    print("🐍 Testing Python version...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True
    else:
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} (3.8+ required)")
        return False

def test_core_imports():
    """Test core Python imports"""
    print("\n📦 Testing core imports...")
    
    tests = [
        ("json", "JSON handling"),
        ("os", "Operating system interface"),
        ("threading", "Threading support"),
        ("queue", "Queue operations"),
        ("datetime", "Date and time"),
        ("time", "Time functions"),
        ("requests", "HTTP requests")
    ]
    
    results = {}
    for module, description in tests:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
            results[module] = True
        except ImportError:
            print(f"❌ {module} - {description} (Missing)")
            results[module] = False
    
    return results

def test_voice_components():
    """Test voice-related imports"""
    print("\n🎤 Testing voice components...")
    
    # Test speech recognition
    try:
        import speech_recognition as sr
        print("✅ speechrecognition - Voice input")
        
        # Test microphone
        try:
            mic = sr.Microphone()
            print("✅ Microphone access")
        except Exception as e:
            print(f"⚠️ Microphone issue: {e}")
            
    except ImportError:
        print("❌ speechrecognition - Install with: pip install speechrecognition")
    
    # Test TTS
    try:
        import pyttsx3
        engine = pyttsx3.init()
        print("✅ pyttsx3 - Text-to-speech")
        engine.stop()
    except ImportError:
        print("❌ pyttsx3 - Install with: pip install pyttsx3")
    except Exception as e:
        print(f"⚠️ TTS issue: {e}")

def test_gui_components():
    """Test GUI-related imports"""
    print("\n🎨 Testing GUI components...")
    
    # Test Kivy
    try:
        import kivy
        print(f"✅ Kivy {kivy.__version__} - GUI framework")
        
        # Test specific Kivy components
        try:
            from kivy.app import App
            from kivy.uix.label import Label
            print("✅ Kivy components")
        except ImportError as e:
            print(f"⚠️ Kivy components issue: {e}")
            
    except ImportError:
        print("❌ Kivy - Install with: pip install kivy")
    
    # Test KivyMD
    try:
        import kivymd
        print("✅ KivyMD - Material Design")
    except ImportError:
        print("❌ KivyMD - Install with: pip install kivymd")
    
    # Test PIL
    try:
        from PIL import Image
        print("✅ Pillow - Image processing")
    except ImportError:
        print("❌ Pillow - Install with: pip install pillow")
    
    # Test NumPy
    try:
        import numpy as np
        print("✅ NumPy - Mathematical operations")
    except ImportError:
        print("❌ NumPy - Install with: pip install numpy")

def test_ai_core():
    """Test AI core functionality"""
    print("\n🧠 Testing AI core...")
    
    try:
        from ai_core import GeminiAI
        ai = GeminiAI()
        print("✅ AI core import successful")
        
        # Test config file
        if os.path.exists('config.json'):
            with open('config.json', 'r') as f:
                config = json.load(f)
                if config.get('gemini_api_key'):
                    print("✅ Gemini API key configured")
                else:
                    print("⚠️ Gemini API key not configured")
        else:
            print("⚠️ Config file not found")
            
    except Exception as e:
        print(f"❌ AI core error: {e}")

def test_voice_engine():
    """Test voice engine"""
    print("\n🎙️ Testing voice engine...")
    
    try:
        from voice_engine import VoiceEngine
        voice = VoiceEngine()
        voice.initialize()
        print("✅ Voice engine initialized")
    except Exception as e:
        print(f"❌ Voice engine error: {e}")

def test_automation():
    """Test automation components"""
    print("\n🔧 Testing automation...")
    
    try:
        from automation_tasks import AutomationManager
        automation = AutomationManager()
        print("✅ Automation manager created")
        
        # Test file operations
        result = automation.execute_task('list_files', directory='.')
        if result['success']:
            print("✅ File operations working")
        else:
            print(f"⚠️ File operations issue: {result['message']}")
            
    except Exception as e:
        print(f"❌ Automation error: {e}")

def test_gui_main():
    """Test main GUI application"""
    print("\n🖥️ Testing main GUI...")
    
    try:
        # Import without running
        import jarvis_main
        print("✅ Main GUI import successful")
    except Exception as e:
        print(f"❌ Main GUI error: {e}")
        print("Traceback:", traceback.format_exc())

def create_test_config():
    """Create test configuration"""
    print("\n⚙️ Creating test configuration...")
    
    config = {
        "gemini_api_key": "test_key_replace_with_real",
        "voice_settings": {
            "recognition_timeout": 5,
            "phrase_timeout": 10,
            "tts_rate": 180,
            "tts_volume": 0.9,
            "tts_type": "pyttsx3"
        },
        "email_config": {
            "email": "<EMAIL>",
            "password": "test_password",
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587
        }
    }
    
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Test configuration created")

def run_simple_test():
    """Run simple JARVIS test"""
    print("\n🤖 Running simple JARVIS test...")
    
    try:
        from jarvis_simple import JarvisSimple
        print("✅ Simple JARVIS import successful")
        
        # Test AI responses
        from jarvis_simple import SimpleAI
        ai = SimpleAI()
        ai.initialize()
        
        test_messages = [
            "hello",
            "what can you do",
            "goodbye"
        ]
        
        for msg in test_messages:
            response = ai.get_response(msg)
            print(f"  👤 {msg}")
            print(f"  🤖 {response[:50]}...")
        
        print("✅ Simple AI responses working")
        
    except Exception as e:
        print(f"❌ Simple JARVIS error: {e}")

def generate_report(results):
    """Generate test report"""
    print("\n📊 TEST REPORT")
    print("=" * 50)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 Recommendations:")
    
    if not results.get('requests', True):
        print("• Install requests: pip install requests")
    
    if not results.get('voice', True):
        print("• Install voice: pip install speechrecognition pyttsx3")
    
    if not results.get('gui', True):
        print("• Install GUI: pip install kivy kivymd pillow numpy")
    
    if not results.get('config', True):
        print("• Configure Gemini API key in config.json")
    
    print("\n🚀 Next Steps:")
    if passed_tests >= total_tests * 0.7:
        print("• Most components working - ready for basic testing")
        print("• Run: python jarvis_simple.py")
        if results.get('gui', True):
            print("• Run: python jarvis_main.py (if GUI working)")
    else:
        print("• Install missing dependencies first")
        print("• Run: python setup_jarvis.py")

def main():
    """Main test function"""
    print("🧪 JARVIS Comprehensive Test Suite")
    print("=" * 50)
    
    results = {}
    
    # Core tests
    results['python'] = test_python_version()
    core_results = test_core_imports()
    results.update(core_results)
    
    # Component tests
    test_voice_components()
    test_gui_components()
    
    # Module tests
    test_ai_core()
    test_voice_engine()
    test_automation()
    test_gui_main()
    
    # Create test config
    create_test_config()
    results['config'] = True
    
    # Simple test
    run_simple_test()
    results['simple'] = True
    
    # Generate report
    generate_report(results)

if __name__ == "__main__":
    main()
