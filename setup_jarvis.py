#!/usr/bin/env python3
"""
JARVIS Setup Script
Complete installation and configuration for JARVIS AI Assistant
"""

import os
import sys
import subprocess
import json
import platform

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3.8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version}")
    return True

def install_dependencies():
    """Install required packages"""
    print("\n📦 Installing dependencies...")

    # Core dependencies that are essential
    core_packages = [
        "requests>=2.28.0",
        "speechrecognition>=3.10.0",
        "pyttsx3>=2.90"
    ]

    # GUI dependencies (optional)
    gui_packages = [
        "kivy>=2.2.0",
        "kivymd>=1.1.1",
        "pillow>=10.0.0",
        "numpy>=1.24.0"
    ]

    try:
        # Install core packages first
        print("Installing core packages...")
        for package in core_packages:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

        print("✅ Core dependencies installed")

        # Try to install GUI packages
        install_gui = input("Install GUI dependencies (Kivy)? (y/n): ").lower().strip()
        if install_gui.startswith('y'):
            print("Installing GUI packages...")
            for package in gui_packages:
                try:
                    print(f"Installing {package}...")
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                except subprocess.CalledProcessError:
                    print(f"⚠️ Failed to install {package}, continuing...")

            print("✅ GUI dependencies installation attempted")

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        print("You can try installing manually with:")
        print("pip install requests speechrecognition pyttsx3")
        return False

def setup_config():
    """Setup configuration file"""
    print("\n⚙️ Setting up configuration...")
    
    config = {
        "gemini_api_key": "",
        "voice_settings": {
            "recognition_timeout": 5,
            "phrase_timeout": 10,
            "tts_rate": 180,
            "tts_volume": 0.9,
            "tts_type": "pyttsx3"
        },
        "email_config": {
            "email": "",
            "password": "",
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587
        },
        "elevenlabs_api_key": ""
    }
    
    # Get Gemini API key
    print("\n🧠 Gemini API Configuration")
    print("Get your free API key at: https://makersuite.google.com/app/apikey")
    
    api_key = input("Enter your Gemini API key (or press Enter to skip): ").strip()
    if api_key:
        config["gemini_api_key"] = api_key
        print("✅ Gemini API key configured")
    else:
        print("⚠️ You can add the API key later in config.json")
    
    # Optional: ElevenLabs API key
    print("\n🎤 Advanced TTS (Optional)")
    print("For premium voice quality, get ElevenLabs API key at: https://elevenlabs.io")
    
    elevenlabs_key = input("Enter ElevenLabs API key (or press Enter to skip): ").strip()
    if elevenlabs_key:
        config["elevenlabs_api_key"] = elevenlabs_key
        config["voice_settings"]["tts_type"] = "elevenlabs"
        print("✅ ElevenLabs TTS configured")
    
    # Save config
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Configuration saved to config.json")

def test_components():
    """Test core components"""
    print("\n🧪 Testing components...")
    
    # Test Kivy
    try:
        import kivy
        print("✅ Kivy GUI framework")
    except ImportError:
        print("❌ Kivy not available")
        return False
    
    # Test speech recognition
    try:
        import speech_recognition as sr
        print("✅ Speech recognition")
    except ImportError:
        print("❌ Speech recognition not available")
    
    # Test TTS
    try:
        import pyttsx3
        print("✅ Text-to-speech")
    except ImportError:
        print("❌ Text-to-speech not available")
    
    # Test requests
    try:
        import requests
        print("✅ HTTP requests")
    except ImportError:
        print("❌ Requests not available")
        return False
    
    return True

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if platform.system() == "Windows":
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "JARVIS AI Assistant.lnk")
            target = os.path.join(os.getcwd(), "jarvis_main.py")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = os.getcwd()
            shortcut.save()
            
            print("✅ Desktop shortcut created")
            
        except ImportError:
            print("⚠️ Desktop shortcut requires: pip install winshell pywin32")
        except Exception as e:
            print(f"⚠️ Could not create shortcut: {e}")

def run_demo():
    """Run JARVIS demo"""
    demo = input("\nWould you like to run JARVIS now? (y/n): ").lower().strip()
    
    if demo.startswith('y'):
        print("\n🚀 Starting JARVIS...")
        try:
            subprocess.run([sys.executable, "jarvis_main.py"])
        except KeyboardInterrupt:
            print("\n👋 JARVIS shutdown")
        except Exception as e:
            print(f"❌ Error starting JARVIS: {e}")

def main():
    """Main setup function"""
    print("🤖 JARVIS AI Assistant Setup")
    print("=" * 50)
    print("Setting up your futuristic AI assistant...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Setup failed during installation")
        sys.exit(1)
    
    # Setup configuration
    setup_config()
    
    # Test components
    if not test_components():
        print("⚠️ Some components may not work properly")
        continue_setup = input("Continue anyway? (y/n): ").lower().strip()
        if not continue_setup.startswith('y'):
            sys.exit(1)
    
    # Create shortcuts
    create_desktop_shortcut()
    
    # Success message
    print("\n🎉 JARVIS setup completed successfully!")
    print("\n📋 What's available:")
    print("• Main GUI: python jarvis_main.py")
    print("• Test AI: python ai_core.py")
    print("• Test Voice: python voice_engine.py")
    print("• Test Automation: python automation_tasks.py")
    
    print("\n💡 Quick start:")
    print("1. Make sure you have a Gemini API key in config.json")
    print("2. Run: python jarvis_main.py")
    print("3. Wait for boot sequence to complete")
    print("4. Click microphone or type to interact")
    
    print("\n🎯 Features:")
    print("• Voice input/output with waveform animation")
    print("• Gemini AI integration with JARVIS personality")
    print("• Futuristic animated GUI with sci-fi effects")
    print("• Automation tasks (email, reminders, browser, files)")
    print("• Mobile-compatible (Android via Buildozer)")
    
    # Offer to run demo
    run_demo()

if __name__ == "__main__":
    main()
