#!/usr/bin/env python3
"""
Setup script for JARVIS GUI
Handles installation, configuration, and first-time setup
"""

import os
import sys
import subprocess
import platform
from config import JarvisConfig

def check_python_version():
    """Check Python version compatibility"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required for JARVIS GUI")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_gui_dependencies():
    """Install GUI-specific dependencies"""
    print("\n📦 Installing GUI dependencies...")
    
    gui_packages = [
        "customtkinter>=5.2.0",
        "pillow>=10.0.0",
        "numpy>=1.24.0",
        "requests>=2.28.0"
    ]
    
    try:
        for package in gui_packages:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        
        print("✅ GUI dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install GUI dependencies: {e}")
        return False

def install_voice_dependencies():
    """Install voice-related dependencies"""
    print("\n🎤 Installing voice dependencies...")
    
    voice_packages = [
        "speechrecognition>=3.10.0",
        "pyttsx3>=2.90"
    ]
    
    try:
        for package in voice_packages:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        
        # Install pyaudio with platform-specific instructions
        print("Installing pyaudio...")
        if platform.system() == "Windows":
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyaudio"])
        else:
            print("⚠️  For voice features on macOS/Linux:")
            print("   macOS: brew install portaudio && pip install pyaudio")
            print("   Ubuntu/Debian: sudo apt-get install python3-pyaudio")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyaudio"])
            except subprocess.CalledProcessError:
                print("⚠️  PyAudio installation failed. Voice features may not work.")
        
        print("✅ Voice dependencies installed!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Voice dependencies installation had issues: {e}")
        print("You can still use the GUI without voice features.")
        return True

def install_mobile_dependencies():
    """Install mobile GUI dependencies (optional)"""
    print("\n📱 Installing mobile GUI dependencies (optional)...")
    
    mobile_packages = [
        "kivy>=2.2.0",
        "kivymd>=1.1.1"
    ]
    
    install_mobile = input("Install mobile GUI support? (y/n): ").lower().strip()
    
    if install_mobile.startswith('y'):
        try:
            for package in mobile_packages:
                print(f"Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            
            print("✅ Mobile GUI dependencies installed!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Mobile dependencies installation failed: {e}")
            return False
    else:
        print("⏭️  Skipping mobile GUI dependencies")
        return True

def test_gui_components():
    """Test if GUI components are working"""
    print("\n🧪 Testing GUI components...")
    
    try:
        # Test CustomTkinter
        print("Testing CustomTkinter...")
        import customtkinter as ctk
        print("✅ CustomTkinter working")
        
        # Test PIL
        print("Testing PIL/Pillow...")
        from PIL import Image, ImageTk
        print("✅ PIL/Pillow working")
        
        # Test numpy
        print("Testing NumPy...")
        import numpy as np
        print("✅ NumPy working")
        
        # Test voice components
        print("Testing voice components...")
        try:
            import speech_recognition as sr
            import pyttsx3
            print("✅ Voice components working")
        except ImportError:
            print("⚠️  Voice components not available")
        
        # Test requests
        print("Testing requests...")
        import requests
        print("✅ Requests working")
        
        return True
        
    except ImportError as e:
        print(f"❌ Component test failed: {e}")
        return False

def setup_jarvis_config():
    """Setup JARVIS configuration"""
    print("\n⚙️  Setting up JARVIS configuration...")
    
    config = JarvisConfig()
    
    print("JARVIS needs a Gemini API key to function.")
    print("You can get a free API key at: https://makersuite.google.com/app/apikey")
    
    setup_now = input("Would you like to configure the API key now? (y/n): ").lower().strip()
    
    if setup_now.startswith('y'):
        config.setup_gemini_api()
    else:
        print("⏭️  You can configure the API key later by running: python config.py")
    
    # Basic voice settings
    print("\nConfiguring voice settings...")
    config.set("voice.voice_enabled", True)
    config.set("voice.tts_rate", 180)
    config.set("voice.tts_volume", 0.9)
    
    # GUI settings
    print("Configuring GUI settings...")
    config.set("gui.theme", "dark")
    config.set("gui.window_width", 1200)
    config.set("gui.window_height", 800)
    
    print("✅ Basic configuration complete!")

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if platform.system() == "Windows":
        create_shortcut = input("Create desktop shortcut? (y/n): ").lower().strip()
        
        if create_shortcut.startswith('y'):
            try:
                import winshell
                from win32com.client import Dispatch
                
                desktop = winshell.desktop()
                path = os.path.join(desktop, "JARVIS AI Assistant.lnk")
                target = os.path.join(os.getcwd(), "jarvis_gui.py")
                wDir = os.getcwd()
                icon = target
                
                shell = Dispatch('WScript.Shell')
                shortcut = shell.CreateShortCut(path)
                shortcut.Targetpath = sys.executable
                shortcut.Arguments = f'"{target}"'
                shortcut.WorkingDirectory = wDir
                shortcut.IconLocation = icon
                shortcut.save()
                
                print("✅ Desktop shortcut created!")
                
            except ImportError:
                print("⚠️  Desktop shortcut creation requires: pip install winshell pywin32")
            except Exception as e:
                print(f"⚠️  Could not create desktop shortcut: {e}")

def run_demo():
    """Run a quick demo of the GUI"""
    demo = input("Would you like to run a quick demo? (y/n): ").lower().strip()
    
    if demo.startswith('y'):
        print("\n🚀 Starting JARVIS GUI demo...")
        try:
            import jarvis_gui
            app = jarvis_gui.JarvisGUI()
            
            # Add demo message
            app.add_message("JARVIS", "Welcome to the JARVIS GUI demo! This is your futuristic AI assistant interface.", "assistant")
            app.add_message("Demo", "Try clicking the microphone button or typing a message!", "assistant")
            
            app.run()
            
        except Exception as e:
            print(f"❌ Demo failed: {e}")
            print("You can manually start JARVIS with: python jarvis_gui.py")

def main():
    """Main setup function"""
    print("🚀 JARVIS GUI Setup")
    print("=" * 50)
    print("Setting up your futuristic AI assistant interface...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    
    if not install_gui_dependencies():
        print("❌ GUI setup failed. Please check the errors above.")
        sys.exit(1)
    
    install_voice_dependencies()
    install_mobile_dependencies()
    
    # Test components
    if not test_gui_components():
        print("⚠️  Some components may not work properly.")
        continue_setup = input("Continue anyway? (y/n): ").lower().strip()
        if not continue_setup.startswith('y'):
            sys.exit(1)
    
    # Setup configuration
    setup_jarvis_config()
    
    # Create shortcuts (Windows only)
    create_desktop_shortcut()
    
    # Success message
    print("\n🎉 JARVIS GUI setup completed successfully!")
    print("\n📋 What's available:")
    print("• Desktop GUI: python jarvis_gui.py")
    print("• Mobile GUI: python jarvis_mobile.py")
    print("• Configuration: python config.py")
    print("• Original CLI: python assistant.py")
    
    print("\n💡 Quick start:")
    print("1. Make sure you have a Gemini API key configured")
    print("2. Run: python jarvis_gui.py")
    print("3. Click the microphone button or type a message")
    print("4. Enjoy your futuristic AI assistant!")
    
    # Offer to run demo
    run_demo()

if __name__ == "__main__":
    main()
