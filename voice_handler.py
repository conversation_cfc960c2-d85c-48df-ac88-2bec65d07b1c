"""
Voice Handler for the Personal Assistant
Handles speech recognition and text-to-speech functionality.
"""

import logging
from typing import Optional


class VoiceHandler:
    """Handle voice input and output for the assistant."""
    
    def __init__(self):
        """Initialize voice handler with speech recognition and TTS."""
        self.logger = logging.getLogger(__name__)
        self.recognizer = None
        self.microphone = None
        self.tts_engine = None
        self.voice_available = False
        
        self._initialize_voice_components()
    
    def _initialize_voice_components(self):
        """Initialize speech recognition and text-to-speech components."""
        try:
            # Initialize speech recognition
            import speech_recognition as sr
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            print("🎤 Calibrating microphone for ambient noise...")
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            print("✅ Microphone calibrated!")
            
            # Initialize text-to-speech
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            
            # Configure TTS settings
            self._configure_tts()
            
            self.voice_available = True
            print("🔊 Voice capabilities initialized successfully!")
            
        except ImportError as e:
            print("⚠️  Voice libraries not found. Install with: pip install speechrecognition pyttsx3 pyaudio")
            self.logger.warning(f"Voice libraries not available: {e}")
            self.voice_available = False
            
        except Exception as e:
            print(f"⚠️  Error initializing voice components: {e}")
            self.logger.error(f"Voice initialization error: {e}")
            self.voice_available = False
    
    def _configure_tts(self):
        """Configure text-to-speech settings."""
        if not self.tts_engine:
            return
        
        try:
            # Get available voices
            voices = self.tts_engine.getProperty('voices')
            
            # Try to set a pleasant voice (prefer female voices for friendliness)
            for voice in voices:
                if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                    self.tts_engine.setProperty('voice', voice.id)
                    break
            
            # Set speech rate (words per minute)
            self.tts_engine.setProperty('rate', 180)  # Slightly slower for clarity
            
            # Set volume (0.0 to 1.0)
            self.tts_engine.setProperty('volume', 0.9)
            
        except Exception as e:
            self.logger.warning(f"Could not configure TTS settings: {e}")
    
    def listen(self, timeout: int = 5, phrase_timeout: int = 2) -> str:
        """
        Listen for voice input and convert to text.
        
        Args:
            timeout: Maximum time to wait for speech to start
            phrase_timeout: Maximum time to wait for phrase to complete
            
        Returns:
            Recognized text or empty string if recognition failed
        """
        if not self.voice_available:
            return input("Voice not available. Please type: ")
        
        try:
            print("🎤 Listening... (speak now)")
            
            # Listen for audio input
            with self.microphone as source:
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout, 
                    phrase_time_limit=phrase_timeout
                )
            
            print("🔄 Processing speech...")
            
            # Recognize speech using Google's service
            try:
                text = self.recognizer.recognize_google(audio)
                print(f"👤 You said: {text}")
                return text
                
            except Exception as e:
                # Try alternative recognition services
                try:
                    text = self.recognizer.recognize_sphinx(audio)
                    print(f"👤 You said: {text}")
                    return text
                except:
                    pass
                
                print("❌ Sorry, I couldn't understand what you said.")
                return ""
                
        except Exception as e:
            print(f"❌ Error during speech recognition: {e}")
            self.logger.error(f"Speech recognition error: {e}")
            return ""
    
    def speak(self, text: str):
        """
        Convert text to speech and play it.
        
        Args:
            text: Text to convert to speech
        """
        if not self.voice_available or not self.tts_engine:
            return
        
        try:
            # Clean text for better speech
            clean_text = self._clean_text_for_speech(text)
            
            # Speak the text
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            
        except Exception as e:
            self.logger.error(f"Text-to-speech error: {e}")
    
    def _clean_text_for_speech(self, text: str) -> str:
        """
        Clean text to make it more suitable for speech synthesis.
        
        Args:
            text: Original text
            
        Returns:
            Cleaned text optimized for speech
        """
        # Remove emojis and special characters that don't speak well
        import re
        
        # Remove emoji patterns
        emoji_pattern = re.compile("["
                                 u"\U0001F600-\U0001F64F"  # emoticons
                                 u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                 u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                 u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                                 u"\U00002702-\U000027B0"
                                 u"\U000024C2-\U0001F251"
                                 "]+", flags=re.UNICODE)
        
        clean_text = emoji_pattern.sub('', text)
        
        # Replace common symbols with words
        replacements = {
            '&': 'and',
            '@': 'at',
            '#': 'hashtag',
            '$': 'dollars',
            '%': 'percent',
            '+': 'plus',
            '=': 'equals',
            '<': 'less than',
            '>': 'greater than',
            '|': 'or',
            '🤖': '',  # Remove robot emoji
            '👤': '',  # Remove user emoji
            '🔔': 'Reminder:',
            '⏰': '',
            '📧': '',
            '🎤': '',
            '🔊': '',
            '✅': '',
            '❌': '',
            '⚠️': 'Warning:',
            '🔄': '',
            '🚀': ''
        }
        
        for symbol, replacement in replacements.items():
            clean_text = clean_text.replace(symbol, replacement)
        
        # Clean up extra spaces
        clean_text = ' '.join(clean_text.split())
        
        return clean_text
    
    def test_voice(self):
        """Test voice functionality."""
        if not self.voice_available:
            print("❌ Voice functionality is not available.")
            return False
        
        print("🧪 Testing voice functionality...")
        
        # Test TTS
        self.speak("Hello! I'm testing my voice. Can you hear me?")
        
        # Test speech recognition
        print("Now I'll test speech recognition. Please say 'Hello assistant'")
        result = self.listen(timeout=10)
        
        if result and 'hello' in result.lower():
            print("✅ Voice test successful!")
            self.speak("Great! Voice functionality is working perfectly.")
            return True
        else:
            print("⚠️  Speech recognition may have issues.")
            return False
    
    def set_voice_settings(self, rate: int = None, volume: float = None):
        """
        Adjust voice settings.
        
        Args:
            rate: Speech rate (words per minute)
            volume: Volume level (0.0 to 1.0)
        """
        if not self.tts_engine:
            return
        
        try:
            if rate is not None:
                self.tts_engine.setProperty('rate', rate)
                print(f"🔊 Speech rate set to {rate} words per minute")
            
            if volume is not None:
                self.tts_engine.setProperty('volume', volume)
                print(f"🔊 Volume set to {volume}")
                
        except Exception as e:
            self.logger.error(f"Error setting voice properties: {e}")
    
    def list_available_voices(self):
        """List all available TTS voices."""
        if not self.tts_engine:
            print("❌ TTS engine not available")
            return
        
        try:
            voices = self.tts_engine.getProperty('voices')
            print("\n🔊 Available voices:")
            
            for i, voice in enumerate(voices):
                print(f"{i + 1}. {voice.name} ({voice.id})")
                print(f"   Languages: {voice.languages}")
                print(f"   Gender: {getattr(voice, 'gender', 'Unknown')}")
                print()
                
        except Exception as e:
            self.logger.error(f"Error listing voices: {e}")
    
    def change_voice(self, voice_index: int):
        """
        Change the TTS voice.
        
        Args:
            voice_index: Index of the voice to use (1-based)
        """
        if not self.tts_engine:
            return
        
        try:
            voices = self.tts_engine.getProperty('voices')
            
            if 1 <= voice_index <= len(voices):
                selected_voice = voices[voice_index - 1]
                self.tts_engine.setProperty('voice', selected_voice.id)
                print(f"🔊 Voice changed to: {selected_voice.name}")
                self.speak("Hello! This is my new voice. How do I sound?")
            else:
                print(f"❌ Invalid voice index. Choose between 1 and {len(voices)}")
                
        except Exception as e:
            self.logger.error(f"Error changing voice: {e}")


# Utility function for testing
def test_voice_handler():
    """Test the voice handler functionality."""
    print("🧪 Testing Voice Handler...")
    
    handler = VoiceHandler()
    
    if handler.voice_available:
        handler.test_voice()
        handler.list_available_voices()
    else:
        print("❌ Voice handler not available for testing")


if __name__ == "__main__":
    test_voice_handler()
