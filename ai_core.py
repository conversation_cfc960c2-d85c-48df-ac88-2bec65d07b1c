#!/usr/bin/env python3
"""
AI Core - Gemini API Integration
Handles all AI conversations and automation logic
"""

import requests
import json
import os
from typing import Optional, Dict, Any

class GeminiAI:
    """Gemini AI integration for JARVIS"""
    
    def __init__(self):
        self.api_key = None
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"
        self.model = "gemini-pro"
        self.conversation_history = []
        self.max_history = 10
        
        # JARVIS personality prompt
        self.system_prompt = """You are J<PERSON><PERSON><PERSON>, an advanced AI assistant inspired by <PERSON>'s AI from Iron Man. 
        You are professional, intelligent, witty, and helpful. You speak in a sophisticated but friendly manner.
        You can help with various tasks including automation, information, and conversation.
        Keep responses concise but informative. Always maintain the JARVIS personality - confident and capable."""
    
    def initialize(self):
        """Initialize Gemini AI with API key"""
        # Try to load API key from environment or config
        self.api_key = os.getenv('GEMINI_API_KEY')
        
        if not self.api_key:
            # Try to load from config file
            try:
                with open('config.json', 'r') as f:
                    config = json.load(f)
                    self.api_key = config.get('gemini_api_key')
            except FileNotFoundError:
                pass
        
        if not self.api_key:
            raise Exception("Gemini API key not found. Please set GEMINI_API_KEY environment variable or add to config.json")
        
        # Test API connection
        self.test_connection()
    
    def test_connection(self):
        """Test Gemini API connection"""
        try:
            response = self.get_response("Hello", test_mode=True)
            if not response:
                raise Exception("API test failed")
        except Exception as e:
            raise Exception(f"Failed to connect to Gemini API: {str(e)}")
    
    def get_response(self, user_message: str, test_mode: bool = False) -> str:
        """Get response from Gemini API"""
        try:
            # Add to conversation history
            if not test_mode:
                self.conversation_history.append({"role": "user", "content": user_message})
                
                # Trim history if too long
                if len(self.conversation_history) > self.max_history * 2:
                    self.conversation_history = self.conversation_history[-self.max_history:]
            
            # Prepare the prompt with context
            if test_mode:
                prompt = "Respond with 'JARVIS systems online' to confirm connection."
            else:
                # Build context from conversation history
                context = self.system_prompt + "\n\nConversation history:\n"
                for msg in self.conversation_history[-6:]:  # Last 3 exchanges
                    role = "User" if msg["role"] == "user" else "JARVIS"
                    context += f"{role}: {msg['content']}\n"
                
                context += f"\nUser: {user_message}\nJARVIS:"
                prompt = context
            
            # Make API request
            url = f"{self.base_url}/{self.model}:generateContent?key={self.api_key}"
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'candidates' in result and len(result['candidates']) > 0:
                    ai_response = result['candidates'][0]['content']['parts'][0]['text'].strip()
                    
                    # Add to conversation history
                    if not test_mode:
                        self.conversation_history.append({"role": "assistant", "content": ai_response})
                    
                    return ai_response
                else:
                    return "I apologize, but I couldn't generate a proper response."
            
            else:
                error_msg = f"API Error {response.status_code}"
                if response.text:
                    try:
                        error_data = response.json()
                        if 'error' in error_data:
                            error_msg = error_data['error'].get('message', error_msg)
                    except:
                        pass
                
                raise Exception(error_msg)
                
        except requests.exceptions.Timeout:
            return "I'm taking longer than usual to respond. Please try again."
        
        except requests.exceptions.ConnectionError:
            return "I'm having trouble connecting to my AI systems. Please check your internet connection."
        
        except Exception as e:
            raise Exception(f"AI processing error: {str(e)}")
    
    def analyze_intent(self, message: str) -> Dict[str, Any]:
        """Analyze user intent for automation tasks"""
        message_lower = message.lower()
        
        # Email intent
        if any(word in message_lower for word in ['email', 'mail', 'send message']):
            return {
                'type': 'email',
                'confidence': 0.8,
                'entities': self.extract_email_entities(message)
            }
        
        # Reminder intent
        elif any(word in message_lower for word in ['remind', 'reminder', 'remember', 'schedule']):
            return {
                'type': 'reminder',
                'confidence': 0.8,
                'entities': self.extract_reminder_entities(message)
            }
        
        # Web browsing intent
        elif any(word in message_lower for word in ['open', 'browse', 'website', 'search']):
            return {
                'type': 'web',
                'confidence': 0.7,
                'entities': self.extract_web_entities(message)
            }
        
        # File operations intent
        elif any(word in message_lower for word in ['file', 'folder', 'rename', 'delete', 'move']):
            return {
                'type': 'file',
                'confidence': 0.7,
                'entities': self.extract_file_entities(message)
            }
        
        # General conversation
        else:
            return {
                'type': 'conversation',
                'confidence': 1.0,
                'entities': {}
            }
    
    def extract_email_entities(self, message: str) -> Dict[str, str]:
        """Extract email-related entities"""
        # Simple entity extraction - can be enhanced with NLP
        entities = {}
        
        # Look for email addresses
        import re
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, message)
        if emails:
            entities['recipient'] = emails[0]
        
        # Look for names
        if 'to ' in message.lower():
            parts = message.lower().split('to ')
            if len(parts) > 1:
                name_part = parts[1].split()[0]
                entities['recipient_name'] = name_part
        
        return entities
    
    def extract_reminder_entities(self, message: str) -> Dict[str, str]:
        """Extract reminder-related entities"""
        entities = {}
        
        # Look for time expressions
        import re
        time_patterns = [
            r'\d+\s*(minute|hour|day)s?',
            r'(tomorrow|today|tonight)',
            r'\d{1,2}:\d{2}',
            r'(morning|afternoon|evening|night)'
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, message.lower())
            if matches:
                entities['time'] = matches[0]
                break
        
        return entities
    
    def extract_web_entities(self, message: str) -> Dict[str, str]:
        """Extract web-related entities"""
        entities = {}
        
        # Look for URLs
        import re
        url_pattern = r'https?://[^\s]+'
        urls = re.findall(url_pattern, message)
        if urls:
            entities['url'] = urls[0]
        
        # Look for website names
        if 'open ' in message.lower():
            parts = message.lower().split('open ')
            if len(parts) > 1:
                site_part = parts[1].split()[0]
                entities['website'] = site_part
        
        return entities
    
    def extract_file_entities(self, message: str) -> Dict[str, str]:
        """Extract file-related entities"""
        entities = {}
        
        # Look for file paths or names
        import re
        file_pattern = r'[^\s]+\.[a-zA-Z]{2,4}'
        files = re.findall(file_pattern, message)
        if files:
            entities['filename'] = files[0]
        
        return entities
    
    def get_automation_response(self, intent: Dict[str, Any], user_message: str) -> str:
        """Generate response for automation tasks"""
        intent_type = intent['type']
        entities = intent['entities']
        
        if intent_type == 'email':
            if 'recipient' in entities or 'recipient_name' in entities:
                return f"I'll help you send an email. What would you like to say?"
            else:
                return "I'll help you send an email. Who should I send it to?"
        
        elif intent_type == 'reminder':
            if 'time' in entities:
                return f"I'll set a reminder for {entities['time']}. What should I remind you about?"
            else:
                return "I'll set a reminder for you. When would you like to be reminded?"
        
        elif intent_type == 'web':
            if 'url' in entities:
                return f"Opening {entities['url']} for you."
            elif 'website' in entities:
                return f"Opening {entities['website']} for you."
            else:
                return "What website would you like me to open?"
        
        elif intent_type == 'file':
            return "I'll help you with file operations. What would you like me to do?"
        
        else:
            return self.get_response(user_message)

def create_config_file():
    """Create config file template"""
    config = {
        "gemini_api_key": "YOUR_GEMINI_API_KEY_HERE",
        "voice_settings": {
            "recognition_timeout": 5,
            "tts_rate": 180
        }
    }
    
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("Created config.json template. Please add your Gemini API key.")

if __name__ == "__main__":
    # Test the AI core
    try:
        ai = GeminiAI()
        ai.initialize()
        response = ai.get_response("Hello JARVIS")
        print(f"JARVIS: {response}")
    except Exception as e:
        print(f"Error: {e}")
        create_config_file()
