{"api": {"gemini_api_key": "AIzaSyDnQtWScIygyDAnYEPGBmcJG1Q-2J7pnSY", "gemini_model": "gemini-pro", "gemini_endpoint": "https://generativelanguage.googleapis.com/v1beta/models", "timeout": 30, "max_tokens": 1000}, "voice": {"recognition_timeout": 5, "phrase_timeout": 10, "tts_rate": 180, "tts_volume": 0.9, "voice_enabled": true}, "gui": {"theme": "dark", "window_width": 1200, "window_height": 800, "animation_speed": 50, "particle_count": 50}, "assistant": {"name": "JARVIS", "personality": "professional", "response_style": "concise", "max_history": 100}, "features": {"email_enabled": true, "reminders_enabled": true, "file_operations_enabled": true, "web_browsing_enabled": true, "voice_commands_enabled": true}}