"""
Automation modules for the Personal Assistant
Contains classes for handling various automation tasks like email, reminders, browser, and file operations.
"""

import os
import smtplib
import webbrowser
import schedule
import time
import threading
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any
import re
import json


class EmailAutomation:
    """Handle email sending functionality."""
    
    def __init__(self):
        """Initialize email automation with configuration."""
        self.config_file = "email_config.json"
        self.config = self._load_email_config()
    
    def _load_email_config(self) -> Dict[str, str]:
        """Load email configuration from file or create default."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        
        # Return default config
        return {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "email": "",
            "password": "",
            "use_app_password": True
        }
    
    def setup_email_config(self):
        """Interactive setup for email configuration."""
        print("\n📧 Email Configuration Setup")
        print("Note: For Gmail, use an App Password instead of your regular password.")
        print("Go to: Google Account > Security > 2-Step Verification > App passwords")
        
        email = input("Your email address: ")
        password = input("Your email password (or app password): ")
        
        self.config.update({
            "email": email,
            "password": password
        })
        
        # Save configuration
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        print("✅ Email configuration saved!")
    
    def send_email(self, recipient: str, subject: str, message: str) -> Dict[str, Any]:
        """Send an email to the specified recipient."""
        try:
            # Check if email is configured
            if not self.config.get("email") or not self.config.get("password"):
                return {
                    "success": False,
                    "message": "Email not configured. Please run setup first."
                }
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.config["email"]
            msg['To'] = recipient
            msg['Subject'] = subject
            
            # Add body to email
            msg.attach(MIMEText(message, 'plain'))
            
            # Create SMTP session
            server = smtplib.SMTP(self.config["smtp_server"], self.config["smtp_port"])
            server.starttls()  # Enable security
            server.login(self.config["email"], self.config["password"])
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.config["email"], recipient, text)
            server.quit()
            
            return {
                "success": True,
                "message": f"Email sent successfully to {recipient}!"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to send email: {str(e)}"
            }


class ReminderAutomation:
    """Handle reminder and scheduling functionality."""
    
    def __init__(self):
        """Initialize reminder system."""
        self.reminders = []
        self.scheduler_thread = None
        self.running = False
    
    def set_reminder(self, text: str, time: str) -> Dict[str, Any]:
        """Set a reminder for the specified time."""
        try:
            # Parse the time input
            reminder_time = self._parse_time_input(time)
            
            if not reminder_time:
                return {
                    "success": False,
                    "message": "I couldn't understand the time format. Try '5 minutes', '2 hours', or 'tomorrow 9am'"
                }
            
            # Create reminder
            reminder = {
                "text": text,
                "time": reminder_time,
                "created": datetime.now()
            }
            
            self.reminders.append(reminder)
            
            # Start scheduler if not running
            if not self.running:
                self._start_scheduler()
            
            # Schedule the reminder
            if reminder_time <= datetime.now() + timedelta(days=1):
                schedule.every().minute.do(self._check_reminders)
            
            time_str = reminder_time.strftime("%Y-%m-%d %H:%M")
            return {
                "success": True,
                "message": f"Reminder set for {time_str}: '{text}'"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to set reminder: {str(e)}"
            }
    
    def _parse_time_input(self, time_input: str) -> datetime:
        """Parse natural language time input into datetime."""
        now = datetime.now()
        time_input = time_input.lower().strip()
        
        # Handle relative times
        if "minute" in time_input:
            minutes = int(re.search(r'(\d+)', time_input).group(1))
            return now + timedelta(minutes=minutes)
        
        elif "hour" in time_input:
            hours = int(re.search(r'(\d+)', time_input).group(1))
            return now + timedelta(hours=hours)
        
        elif "tomorrow" in time_input:
            tomorrow = now + timedelta(days=1)
            if "am" in time_input or "pm" in time_input:
                # Extract time
                time_match = re.search(r'(\d+)(?::(\d+))?\s*(am|pm)', time_input)
                if time_match:
                    hour = int(time_match.group(1))
                    minute = int(time_match.group(2)) if time_match.group(2) else 0
                    is_pm = time_match.group(3) == "pm"
                    
                    if is_pm and hour != 12:
                        hour += 12
                    elif not is_pm and hour == 12:
                        hour = 0
                    
                    return tomorrow.replace(hour=hour, minute=minute, second=0, microsecond=0)
            else:
                return tomorrow.replace(hour=9, minute=0, second=0, microsecond=0)
        
        # Default: 5 minutes from now
        return now + timedelta(minutes=5)
    
    def _start_scheduler(self):
        """Start the reminder scheduler in a separate thread."""
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
    
    def _run_scheduler(self):
        """Run the scheduler loop."""
        while self.running:
            schedule.run_pending()
            time.sleep(1)
    
    def _check_reminders(self):
        """Check if any reminders are due."""
        now = datetime.now()
        due_reminders = []
        
        for reminder in self.reminders[:]:
            if reminder["time"] <= now:
                due_reminders.append(reminder)
                self.reminders.remove(reminder)
        
        for reminder in due_reminders:
            self._trigger_reminder(reminder)
    
    def _trigger_reminder(self, reminder: Dict[str, Any]):
        """Trigger a reminder notification."""
        print(f"\n🔔 REMINDER: {reminder['text']}")
        print(f"⏰ Scheduled for: {reminder['time'].strftime('%Y-%m-%d %H:%M')}")
        
        # You could add more notification methods here:
        # - System notifications
        # - Sound alerts
        # - Email notifications


class BrowserAutomation:
    """Handle browser and web-related automation."""
    
    def open_url(self, url: str) -> Dict[str, Any]:
        """Open a URL in the default browser."""
        try:
            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # Open URL
            webbrowser.open(url)
            
            return {
                "success": True,
                "message": f"Opened {url} in your default browser!"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to open URL: {str(e)}"
            }
    
    def search_google(self, query: str) -> Dict[str, Any]:
        """Search Google for the given query."""
        try:
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            webbrowser.open(search_url)
            
            return {
                "success": True,
                "message": f"Searched Google for '{query}'"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to search: {str(e)}"
            }


class FileAutomation:
    """Handle file and directory operations."""
    
    def rename_file(self, file_path: str, new_name: str) -> Dict[str, Any]:
        """Rename a file or directory."""
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": f"File '{file_path}' not found."
                }
            
            # Get directory and create new path
            directory = os.path.dirname(file_path)
            new_path = os.path.join(directory, new_name)
            
            # Check if new name already exists
            if os.path.exists(new_path):
                return {
                    "success": False,
                    "message": f"A file named '{new_name}' already exists."
                }
            
            # Rename the file
            os.rename(file_path, new_path)
            
            return {
                "success": True,
                "message": f"Successfully renamed '{file_path}' to '{new_name}'"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to rename file: {str(e)}"
            }
    
    def list_files(self, directory: str = ".") -> Dict[str, Any]:
        """List files in a directory."""
        try:
            if not os.path.exists(directory):
                return {
                    "success": False,
                    "message": f"Directory '{directory}' not found."
                }
            
            files = os.listdir(directory)
            files_info = []
            
            for file in files:
                file_path = os.path.join(directory, file)
                is_dir = os.path.isdir(file_path)
                size = os.path.getsize(file_path) if not is_dir else 0
                
                files_info.append({
                    "name": file,
                    "is_directory": is_dir,
                    "size": size
                })
            
            return {
                "success": True,
                "message": f"Found {len(files)} items in '{directory}'",
                "data": files_info
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to list files: {str(e)}"
            }
    
    def create_directory(self, directory_path: str) -> Dict[str, Any]:
        """Create a new directory."""
        try:
            os.makedirs(directory_path, exist_ok=True)
            
            return {
                "success": True,
                "message": f"Directory '{directory_path}' created successfully!"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to create directory: {str(e)}"
            }
