# JARVIS - Futuristic AI Assistant GUI 🤖

A stunning, Iron Man-inspired GUI for your AI assistant powered by Google's Gemini API. Experience the future of human-AI interaction with voice commands, animated interfaces, and intelligent responses.

## ✨ Features

### 🎨 **Futuristic Interface**
- **Dark sci-fi theme** with glowing blue accents
- **Animated avatar** with pulsing effects and rotating patterns
- **Voice waveform visualization** during speech recognition
- **Smooth animations** and responsive design
- **Chat bubbles** with conversation history
- **Command history panel** showing recent activities

### 🎤 **Advanced Voice Control**
- **Speech recognition** using Google's API
- **Text-to-speech** with natural voice synthesis
- **Real-time waveform** animation during listening
- **Voice activation** with animated microphone button
- **Ambient noise calibration** for better recognition

### 🧠 **AI-Powered Responses**
- **Gemini API integration** for intelligent conversations
- **JARVIS personality** - professional, witty, and helpful
- **Context-aware responses** with conversation memory
- **Configurable AI parameters** (temperature, max tokens)

### 📱 **Multi-Platform Support**
- **Desktop GUI** using CustomTkinter (Windows, macOS, Linux)
- **Mobile GUI** using <PERSON>vy/KivyMD (Android, iOS compatible)
- **Responsive design** that adapts to different screen sizes
- **Touch-friendly controls** for mobile devices

## 🚀 Quick Start

### 1. **Installation**

Run the automated setup:
```bash
python setup_jarvis_gui.py
```

Or install manually:
```bash
pip install customtkinter pillow numpy requests speechrecognition pyttsx3 pyaudio
```

### 2. **Get Gemini API Key**

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a free API key
3. Run configuration: `python config.py`
4. Enter your API key when prompted

### 3. **Launch JARVIS**

**Desktop GUI:**
```bash
python jarvis_gui.py
```

**Mobile GUI:**
```bash
python jarvis_mobile.py
```

**Original CLI:**
```bash
python assistant.py
```

## 🎮 How to Use

### **Voice Commands**
1. Click the **🎤 microphone button**
2. Speak your command when it turns red
3. Watch the waveform animation as you speak
4. JARVIS will respond with voice and text

### **Text Commands**
1. Type in the **text input field** at the bottom
2. Press **Enter** or click **Send**
3. View the conversation in the chat area

### **Example Commands**
- *"Hello JARVIS, how are you today?"*
- *"What's the weather like?"*
- *"Tell me a joke"*
- *"Help me write an email"*
- *"What can you do?"*

## 🔧 Configuration

### **API Settings**
```bash
python config.py
```
- Set Gemini API key
- Configure model parameters
- Test API connection

### **Voice Settings**
- Recognition timeout
- Speech rate and volume
- Voice engine selection

### **GUI Settings**
- Theme (dark/light)
- Window size
- Animation speed
- Particle effects

## 📁 File Structure

```
jarvis-gui/
├── jarvis_gui.py          # Main desktop GUI
├── jarvis_mobile.py       # Mobile-compatible GUI
├── config.py              # Configuration manager
├── setup_jarvis_gui.py    # Automated setup script
├── assistant.py           # Original CLI assistant
├── automation.py          # Automation modules
├── voice_handler.py       # Voice processing
├── logger_config.py       # Logging system
├── requirements.txt       # Dependencies
└── README_GUI.md         # This file
```

## 🎨 GUI Components

### **Desktop Interface (CustomTkinter)**
- **Header**: Title with JARVIS branding
- **Chat Panel**: Scrollable conversation history
- **Avatar Panel**: Animated AI assistant visualization
- **Waveform Display**: Real-time voice activity
- **Control Panel**: Microphone button and text input
- **Status Bar**: System status and time display
- **Command History**: Recent command tracking

### **Mobile Interface (Kivy)**
- **Touch-optimized** buttons and controls
- **Swipe gestures** for navigation
- **Responsive layout** for different screen sizes
- **Material Design** components
- **Optimized animations** for mobile performance

## 🔊 Voice Features

### **Speech Recognition**
- **Google Speech API** for high accuracy
- **Offline fallback** using PocketSphinx
- **Noise cancellation** and ambient adjustment
- **Multiple language support**

### **Text-to-Speech**
- **Natural voice synthesis** using pyttsx3
- **Configurable voice** selection
- **Adjustable speed** and volume
- **Cross-platform compatibility**

## 🤖 AI Integration

### **Gemini API**
```python
# Enhanced JARVIS personality prompt
enhanced_prompt = f"""You are JARVIS, an advanced AI assistant 
inspired by Tony Stark's AI. Respond in a professional, 
intelligent, and slightly witty manner. Be helpful and concise."""
```

### **Features**
- **Context awareness** with conversation history
- **Personality customization** for different response styles
- **Error handling** with graceful fallbacks
- **Rate limiting** and timeout management

## 🎯 Advanced Features

### **Animations**
- **Pulsing avatar** with mathematical sine wave effects
- **Particle systems** for background ambiance
- **Smooth transitions** between states
- **Real-time waveform** visualization

### **Threading**
- **Non-blocking UI** during API calls
- **Background voice processing**
- **Async message handling**
- **Queue-based communication**

### **Customization**
- **Theme switching** (dark/light modes)
- **Color scheme** customization
- **Layout preferences**
- **Feature toggles**

## 🛠️ Development

### **Adding New Features**
1. **Voice Commands**: Extend speech recognition patterns
2. **GUI Elements**: Add new CustomTkinter widgets
3. **AI Responses**: Modify Gemini prompts
4. **Animations**: Create new visual effects

### **Mobile Development**
```bash
# Build for Android using Buildozer
pip install buildozer
buildozer android debug
```

### **Testing**
```bash
python -m pytest tests/
python test_assistant.py
```

## 🔧 Troubleshooting

### **Common Issues**

**Voice not working:**
```bash
# Install audio dependencies
pip install pyaudio speechrecognition pyttsx3

# macOS
brew install portaudio

# Ubuntu/Debian
sudo apt-get install python3-pyaudio
```

**API errors:**
- Check your Gemini API key
- Verify internet connection
- Check API quotas and limits

**GUI not starting:**
- Update CustomTkinter: `pip install --upgrade customtkinter`
- Check Python version (3.8+ required)
- Install missing dependencies

### **Performance Optimization**
- Reduce animation frame rate for slower devices
- Disable particle effects on mobile
- Adjust API timeout settings
- Use smaller AI model variants

## 📱 Mobile Deployment

### **Android (Buildozer)**
```bash
buildozer init
buildozer android debug
```

### **iOS (kivy-ios)**
```bash
pip install kivy-ios
toolchain build python3 kivy
```

## 🎉 What's Next?

### **Planned Features**
- **3D avatar** with facial expressions
- **Gesture recognition** for hands-free control
- **Smart home integration** (lights, temperature, etc.)
- **Calendar and email** management
- **File system** navigation and management
- **Web browsing** integration
- **Multi-language** support

### **Contributing**
Feel free to contribute new features, bug fixes, or improvements!

## 📄 License

This project is open source and available under the MIT License.

---

**Experience the future of AI interaction with JARVIS! 🚀**

*"Sometimes you gotta run before you can walk."* - Tony Stark
