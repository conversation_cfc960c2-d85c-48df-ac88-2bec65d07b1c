#!/usr/bin/env python3
"""
Automation Tasks - Modular automation functions
Email, reminders, browser control, file operations
"""

import smtplib
import webbrowser
import os
import schedule
import time
import threading
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, Optional
import json

class EmailAutomation:
    """Email automation handler"""
    
    def __init__(self):
        self.smtp_config = self.load_email_config()
    
    def load_email_config(self) -> Dict[str, str]:
        """Load email configuration"""
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
                return config.get('email_config', {})
        except FileNotFoundError:
            return {}
    
    def send_email(self, recipient: str, subject: str, message: str) -> Dict[str, Any]:
        """Send email"""
        try:
            if not self.smtp_config:
                return {
                    'success': False,
                    'message': 'Email not configured. Please set up email settings.'
                }
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.smtp_config.get('email')
            msg['To'] = recipient
            msg['Subject'] = subject
            
            msg.attach(MIMEText(message, 'plain'))
            
            # Send email
            server = smtplib.SMTP(
                self.smtp_config.get('smtp_server', 'smtp.gmail.com'),
                self.smtp_config.get('smtp_port', 587)
            )
            server.starttls()
            server.login(
                self.smtp_config.get('email'),
                self.smtp_config.get('password')
            )
            
            text = msg.as_string()
            server.sendmail(self.smtp_config.get('email'), recipient, text)
            server.quit()
            
            return {
                'success': True,
                'message': f'Email sent successfully to {recipient}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Failed to send email: {str(e)}'
            }

class ReminderAutomation:
    """Reminder and scheduling automation"""
    
    def __init__(self):
        self.reminders = []
        self.scheduler_running = False
    
    def set_reminder(self, text: str, when: str) -> Dict[str, Any]:
        """Set a reminder"""
        try:
            reminder_time = self.parse_time(when)
            
            if not reminder_time:
                return {
                    'success': False,
                    'message': 'Could not understand the time format'
                }
            
            reminder = {
                'text': text,
                'time': reminder_time,
                'created': datetime.now()
            }
            
            self.reminders.append(reminder)
            
            # Start scheduler if not running
            if not self.scheduler_running:
                self.start_scheduler()
            
            return {
                'success': True,
                'message': f'Reminder set for {reminder_time.strftime("%Y-%m-%d %H:%M")}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Failed to set reminder: {str(e)}'
            }
    
    def parse_time(self, time_str: str) -> Optional[datetime]:
        """Parse natural language time"""
        now = datetime.now()
        time_str = time_str.lower().strip()
        
        # Handle relative times
        if 'minute' in time_str:
            import re
            match = re.search(r'(\d+)', time_str)
            if match:
                minutes = int(match.group(1))
                return now + timedelta(minutes=minutes)
        
        elif 'hour' in time_str:
            import re
            match = re.search(r'(\d+)', time_str)
            if match:
                hours = int(match.group(1))
                return now + timedelta(hours=hours)
        
        elif 'tomorrow' in time_str:
            tomorrow = now + timedelta(days=1)
            return tomorrow.replace(hour=9, minute=0, second=0, microsecond=0)
        
        # Default: 5 minutes from now
        return now + timedelta(minutes=5)
    
    def start_scheduler(self):
        """Start reminder scheduler"""
        self.scheduler_running = True
        threading.Thread(target=self.scheduler_loop, daemon=True).start()
    
    def scheduler_loop(self):
        """Scheduler main loop"""
        while self.scheduler_running:
            self.check_reminders()
            time.sleep(60)  # Check every minute
    
    def check_reminders(self):
        """Check for due reminders"""
        now = datetime.now()
        due_reminders = []
        
        for reminder in self.reminders[:]:
            if reminder['time'] <= now:
                due_reminders.append(reminder)
                self.reminders.remove(reminder)
        
        for reminder in due_reminders:
            self.trigger_reminder(reminder)
    
    def trigger_reminder(self, reminder: Dict[str, Any]):
        """Trigger a reminder notification"""
        print(f"🔔 REMINDER: {reminder['text']}")
        # Here you could add GUI notification, sound, etc.

class BrowserAutomation:
    """Web browser automation"""
    
    def open_url(self, url: str) -> Dict[str, Any]:
        """Open URL in browser"""
        try:
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            webbrowser.open(url)
            
            return {
                'success': True,
                'message': f'Opened {url} in browser'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Failed to open URL: {str(e)}'
            }
    
    def search_google(self, query: str) -> Dict[str, Any]:
        """Search Google"""
        try:
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            webbrowser.open(search_url)
            
            return {
                'success': True,
                'message': f'Searched Google for: {query}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Failed to search: {str(e)}'
            }

class FileAutomation:
    """File system automation"""
    
    def rename_file(self, old_path: str, new_name: str) -> Dict[str, Any]:
        """Rename a file"""
        try:
            if not os.path.exists(old_path):
                return {
                    'success': False,
                    'message': f'File not found: {old_path}'
                }
            
            directory = os.path.dirname(old_path)
            new_path = os.path.join(directory, new_name)
            
            if os.path.exists(new_path):
                return {
                    'success': False,
                    'message': f'File already exists: {new_name}'
                }
            
            os.rename(old_path, new_path)
            
            return {
                'success': True,
                'message': f'Renamed {old_path} to {new_name}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Failed to rename file: {str(e)}'
            }
    
    def list_files(self, directory: str = ".") -> Dict[str, Any]:
        """List files in directory"""
        try:
            if not os.path.exists(directory):
                return {
                    'success': False,
                    'message': f'Directory not found: {directory}'
                }
            
            files = os.listdir(directory)
            file_list = []
            
            for file in files:
                file_path = os.path.join(directory, file)
                file_info = {
                    'name': file,
                    'is_directory': os.path.isdir(file_path),
                    'size': os.path.getsize(file_path) if not os.path.isdir(file_path) else 0
                }
                file_list.append(file_info)
            
            return {
                'success': True,
                'message': f'Found {len(files)} items in {directory}',
                'data': file_list
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Failed to list files: {str(e)}'
            }
    
    def create_directory(self, path: str) -> Dict[str, Any]:
        """Create a directory"""
        try:
            os.makedirs(path, exist_ok=True)
            
            return {
                'success': True,
                'message': f'Directory created: {path}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Failed to create directory: {str(e)}'
            }

class AutomationManager:
    """Central automation manager"""
    
    def __init__(self):
        self.email = EmailAutomation()
        self.reminders = ReminderAutomation()
        self.browser = BrowserAutomation()
        self.files = FileAutomation()
    
    def execute_task(self, task_type: str, **kwargs) -> Dict[str, Any]:
        """Execute automation task"""
        try:
            if task_type == 'email':
                return self.email.send_email(
                    kwargs.get('recipient'),
                    kwargs.get('subject'),
                    kwargs.get('message')
                )
            
            elif task_type == 'reminder':
                return self.reminders.set_reminder(
                    kwargs.get('text'),
                    kwargs.get('when')
                )
            
            elif task_type == 'open_url':
                return self.browser.open_url(kwargs.get('url'))
            
            elif task_type == 'search':
                return self.browser.search_google(kwargs.get('query'))
            
            elif task_type == 'rename_file':
                return self.files.rename_file(
                    kwargs.get('old_path'),
                    kwargs.get('new_name')
                )
            
            elif task_type == 'list_files':
                return self.files.list_files(kwargs.get('directory', '.'))
            
            elif task_type == 'create_directory':
                return self.files.create_directory(kwargs.get('path'))
            
            else:
                return {
                    'success': False,
                    'message': f'Unknown task type: {task_type}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'Task execution error: {str(e)}'
            }

def create_automation_config():
    """Create automation configuration template"""
    config = {
        "email_config": {
            "email": "<EMAIL>",
            "password": "your_app_password",
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587
        }
    }
    
    try:
        with open('config.json', 'r') as f:
            existing_config = json.load(f)
            existing_config.update(config)
            config = existing_config
    except FileNotFoundError:
        pass
    
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("Automation configuration added to config.json")

if __name__ == "__main__":
    # Test automation
    manager = AutomationManager()
    
    # Test file operations
    result = manager.execute_task('list_files', directory='.')
    print(f"File list: {result}")
    
    # Test browser
    result = manager.execute_task('search', query='Python programming')
    print(f"Search: {result}")
    
    create_automation_config()
