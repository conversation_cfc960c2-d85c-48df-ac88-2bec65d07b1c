#!/usr/bin/env python3
"""
JARVIS - Advanced AI Assistant
Main application entry point with Kivy GUI
"""

import asyncio
import threading
import time
from kivy.app import App
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
from kivy.graphics import Color, Ellipse, Line, Rectangle, Canvas
from kivy.animation import Animation
from kivy.core.window import Window
from kivy.metrics import dp
import queue

from ai_core import GeminiAI
from voice_engine import VoiceEngine
from gui_components import AnimatedBackground, ChatBubble, MicButton, LoadingScreen

# Set window properties
Window.clearcolor = (0.05, 0.05, 0.1, 1)
Window.size = (800, 600)

class JarvisInterface(FloatLayout):
    """Main JARVIS interface"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Initialize components
        self.ai_engine = GeminiAI()
        self.voice_engine = VoiceEngine()
        self.message_queue = queue.Queue()
        
        # State variables
        self.is_booting = True
        self.is_listening = False
        self.chat_messages = []
        
        # Create GUI
        self.setup_gui()
        
        # Start boot sequence
        Clock.schedule_once(self.start_boot_sequence, 0.1)
        
        # Message processing
        Clock.schedule_interval(self.process_messages, 0.1)
    
    def setup_gui(self):
        """Initialize GUI components"""
        # Animated background
        self.background = AnimatedBackground()
        self.add_widget(self.background)
        
        # Loading screen (initially visible)
        self.loading_screen = LoadingScreen()
        self.add_widget(self.loading_screen)
        
        # Main interface (initially hidden)
        self.main_interface = FloatLayout(opacity=0)
        self.setup_main_interface()
        self.add_widget(self.main_interface)
    
    def setup_main_interface(self):
        """Setup main chat interface"""
        # Header
        header = Label(
            text="J.A.R.V.I.S",
            font_size=dp(32),
            color=(0, 0.8, 1, 1),
            size_hint=(1, 0.1),
            pos_hint={'top': 1}
        )
        self.main_interface.add_widget(header)
        
        # Chat area
        self.setup_chat_area()
        
        # Input controls
        self.setup_input_controls()
    
    def setup_chat_area(self):
        """Setup scrollable chat area"""
        # Chat container
        chat_container = BoxLayout(
            orientation='vertical',
            size_hint=(0.9, 0.7),
            pos_hint={'center_x': 0.5, 'top': 0.85}
        )
        
        # Scrollable chat
        self.chat_scroll = ScrollView()
        self.chat_layout = BoxLayout(
            orientation='vertical',
            spacing=dp(10),
            size_hint_y=None,
            height=dp(0)
        )
        self.chat_layout.bind(minimum_height=self.chat_layout.setter('height'))
        
        self.chat_scroll.add_widget(self.chat_layout)
        chat_container.add_widget(self.chat_scroll)
        self.main_interface.add_widget(chat_container)
    
    def setup_input_controls(self):
        """Setup input controls (mic button and text input)"""
        # Input container
        input_container = BoxLayout(
            orientation='horizontal',
            size_hint=(0.9, 0.1),
            pos_hint={'center_x': 0.5, 'y': 0.05},
            spacing=dp(10)
        )
        
        # Microphone button
        self.mic_button = MicButton(
            size_hint=(0.2, 1),
            on_press=self.toggle_voice_input
        )
        input_container.add_widget(self.mic_button)
        
        # Text input
        self.text_input = TextInput(
            hint_text="Type your message...",
            size_hint=(0.6, 1),
            multiline=False,
            background_color=(0.1, 0.1, 0.2, 0.8),
            foreground_color=(1, 1, 1, 1)
        )
        self.text_input.bind(on_text_validate=self.send_text_message)
        input_container.add_widget(self.text_input)
        
        # Send button
        send_button = Button(
            text="Send",
            size_hint=(0.2, 1),
            background_color=(0, 0.8, 1, 1)
        )
        send_button.bind(on_press=self.send_text_message)
        input_container.add_widget(send_button)
        
        self.main_interface.add_widget(input_container)
    
    def start_boot_sequence(self, dt):
        """Start JARVIS boot sequence"""
        self.loading_screen.start_boot_animation()
        Clock.schedule_once(self.complete_boot_sequence, 3.0)
    
    def complete_boot_sequence(self, dt):
        """Complete boot sequence and show main interface"""
        # Fade out loading screen
        anim_out = Animation(opacity=0, duration=1.0)
        anim_out.bind(on_complete=self.remove_loading_screen)
        anim_out.start(self.loading_screen)
        
        # Fade in main interface
        anim_in = Animation(opacity=1, duration=1.0)
        anim_in.start(self.main_interface)
        
        # Initialize AI and voice
        threading.Thread(target=self.initialize_systems, daemon=True).start()
    
    def remove_loading_screen(self, animation, widget):
        """Remove loading screen after animation"""
        self.remove_widget(self.loading_screen)
        self.is_booting = False
        
        # Add welcome message
        self.add_chat_message("JARVIS", "Good day! I'm JARVIS, your AI assistant. How may I help you?", "assistant")
    
    def initialize_systems(self):
        """Initialize AI and voice systems"""
        try:
            self.ai_engine.initialize()
            self.voice_engine.initialize()
            self.message_queue.put(("system_ready", None))
        except Exception as e:
            self.message_queue.put(("system_error", str(e)))
    
    def add_chat_message(self, sender, message, msg_type):
        """Add message to chat"""
        bubble = ChatBubble(sender, message, msg_type)
        self.chat_layout.add_widget(bubble)
        
        # Auto-scroll to bottom
        Clock.schedule_once(lambda dt: setattr(self.chat_scroll, 'scroll_y', 0), 0.1)
        
        # Store message
        self.chat_messages.append({
            'sender': sender,
            'message': message,
            'type': msg_type
        })
    
    def toggle_voice_input(self, instance):
        """Toggle voice input"""
        if not self.is_listening:
            self.start_voice_input()
        else:
            self.stop_voice_input()
    
    def start_voice_input(self):
        """Start voice recognition"""
        if self.is_booting:
            return
        
        self.is_listening = True
        self.mic_button.start_listening_animation()
        
        # Start voice recognition in thread
        threading.Thread(target=self.voice_recognition_thread, daemon=True).start()
    
    def stop_voice_input(self):
        """Stop voice recognition"""
        self.is_listening = False
        self.mic_button.stop_listening_animation()
    
    def voice_recognition_thread(self):
        """Voice recognition in background thread"""
        try:
            text = self.voice_engine.listen()
            if text:
                self.message_queue.put(("voice_input", text))
            else:
                self.message_queue.put(("voice_error", "Could not understand audio"))
        except Exception as e:
            self.message_queue.put(("voice_error", str(e)))
        finally:
            self.message_queue.put(("stop_listening", None))
    
    def send_text_message(self, instance):
        """Send text message"""
        if self.is_booting:
            return
        
        message = self.text_input.text.strip()
        if message:
            self.add_chat_message("You", message, "user")
            self.text_input.text = ""
            
            # Process with AI
            threading.Thread(target=self.process_ai_request, args=(message,), daemon=True).start()
    
    def process_ai_request(self, message):
        """Process message with AI"""
        try:
            response = self.ai_engine.get_response(message)
            self.message_queue.put(("ai_response", response))
        except Exception as e:
            self.message_queue.put(("ai_error", str(e)))
    
    def process_messages(self, dt):
        """Process messages from background threads"""
        try:
            while True:
                msg_type, data = self.message_queue.get_nowait()
                
                if msg_type == "voice_input":
                    self.add_chat_message("You", data, "user")
                    threading.Thread(target=self.process_ai_request, args=(data,), daemon=True).start()
                
                elif msg_type == "ai_response":
                    self.add_chat_message("JARVIS", data, "assistant")
                    # Speak response
                    threading.Thread(target=self.voice_engine.speak, args=(data,), daemon=True).start()
                
                elif msg_type == "stop_listening":
                    self.stop_voice_input()
                
                elif msg_type == "voice_error":
                    self.add_chat_message("System", f"Voice error: {data}", "system")
                    self.stop_voice_input()
                
                elif msg_type == "ai_error":
                    self.add_chat_message("JARVIS", "⚠️ I'm experiencing some technical difficulties.", "assistant")
                
                elif msg_type == "system_ready":
                    self.add_chat_message("System", "All systems online", "system")
                
                elif msg_type == "system_error":
                    self.add_chat_message("System", f"Initialization error: {data}", "system")
                    
        except queue.Empty:
            pass

class JarvisApp(App):
    """Main Kivy application"""
    
    def build(self):
        """Build the application"""
        return JarvisInterface()
    
    def on_start(self):
        """Called when app starts"""
        self.title = "JARVIS - AI Assistant"

def main():
    """Main entry point"""
    JarvisApp().run()

if __name__ == "__main__":
    main()
